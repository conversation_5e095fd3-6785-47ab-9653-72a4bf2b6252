<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>DoxyGen AI - Auto-Generate Documentation</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/atom-one-dark.min.css">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/cpp.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/python.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/java.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/languages/javascript.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
  <style id="app-style">
    .code-diff {
      display: flex;
      width: 100%;
      height: 100%;
    }
    
    .code-diff .column {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      max-height: 500px;
    }
    
    .code-diff pre {
      margin: 0;
      border-radius: 6px;
    }
    
    .progress-bar {
      height: 8px;
      transition: width 0.3s ease;
    }
    
    .terminal {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      background-color: #1e293b;
      color: #e2e8f0;
      border-radius: 8px;
      padding: 12px;
      height: 200px;
      overflow-y: auto;
    }
    
    .terminal-entry {
      margin: 0;
      padding: 3px 0;
      display: flex;
    }
    
    .terminal-entry .timestamp {
      color: #94a3b8;
      margin-right: 8px;
      flex-shrink: 0;
    }
    
    .terminal-entry .message {
      white-space: pre-wrap;
      word-break: break-word;
    }
    
    .terminal-entry .success {
      color: #4ade80;
    }
    
    .terminal-entry .warning {
      color: #fbbf24;
    }
    
    .terminal-entry .error {
      color: #f87171;
    }
    
    /* Custom file input styling */
    .custom-file-input::-webkit-file-upload-button {
      visibility: hidden;
    }
    
    .custom-file-input::before {
      content: 'Choose file';
      display: inline-block;
      background: #1e40af;
      color: white;
      border-radius: 6px;
      padding: 8px 12px;
      outline: none;
      white-space: nowrap;
      cursor: pointer;
      font-weight: 600;
      font-size: 0.875rem;
    }
    
    .custom-file-input:active::before {
      background: #1e3a8a;
    }
    
    .file-tree {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.9em;
    }
    
    .file-tree ul {
      list-style-type: none;
      padding-left: 1.5rem;
    }
    
    .file-tree li {
      padding: 2px 0;
      cursor: pointer;
    }
    
    .file-tree .folder {
      color: #fbbf24;
    }
    
    .file-tree .file {
      color: #e2e8f0;
    }
    
    .file-tree .file:hover, .file-tree .folder:hover {
      text-decoration: underline;
    }
    
    .fade-in {
      animation: fadeIn 0.5s ease-in-out;
    }
    
    @keyframes fadeIn {
      0% { opacity: 0; }
      100% { opacity: 1; }
    }
    
    /* Dark/light mode toggle styles */
    .mode-toggle {
      position: relative;
      width: 60px;
      height: 30px;
    }
    
    .mode-toggle input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .mode-toggle .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #374151;
      transition: .4s;
      border-radius: 34px;
    }
    
    .mode-toggle .slider:before {
      position: absolute;
      content: "";
      height: 22px;
      width: 22px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    .mode-toggle input:checked + .slider {
      background-color: #3b82f6;
    }
    
    .mode-toggle input:checked + .slider:before {
      transform: translateX(30px);
    }
    
    .mode-toggle .icon {
      position: absolute;
      top: 6px;
      z-index: 10;
      color: #1e293b;
      font-size: 0.9rem;
    }
    
    .mode-toggle .icon.sun {
      right: 8px;
    }
    
    .mode-toggle .icon.moon {
      left: 8px;
    }
  </style>
</head>
<body class="bg-gray-100 text-gray-900 dark:bg-gray-900 dark:text-gray-100 min-h-screen">
  <header class="bg-white dark:bg-gray-800 shadow-md">
    <div class="container mx-auto px-4 py-4 flex items-center justify-between">
      <div class="flex items-center">
        <i class="fas fa-file-code text-blue-600 dark:text-blue-400 text-3xl mr-3"></i>
        <h1 class="text-xl font-bold">DoxyGen AI</h1>
      </div>
      <div class="flex items-center space-x-4">
        <label class="mode-toggle">
          <input type="checkbox" id="darkModeToggle">
          <span class="slider">
            <i class="fas fa-sun icon sun"></i>
            <i class="fas fa-moon icon moon"></i>
          </span>
        </label>
        <button id="resetButton" class="bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md flex items-center transition-colors">
          <i class="fas fa-redo-alt mr-2"></i> Start Over
        </button>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-8">
    <!-- Initial View -->
    <div id="initialView" class="fade-in">
      <div class="max-w-4xl mx-auto text-center mb-10">
        <h2 class="text-3xl font-bold mb-4">Generate Doxygen Documentation with AI</h2>
        <p class="text-lg text-gray-600 dark:text-gray-400 mb-8">
          Point to your codebase and we'll automatically generate professional documentation.
        </p>
      </div>
      
      <div class="max-w-5xl mx-auto grid md:grid-cols-2 gap-8">
        <!-- Repository URL Input -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 class="text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-code-branch text-blue-500 mr-2"></i>
            GitHub Repository
          </h3>
          <div class="space-y-4">
            <div>
              <label for="repoUrl" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Repository URL
              </label>
              <input 
                type="text" 
                id="repoUrl" 
                placeholder="https://github.com/username/repository"
                class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
            </div>
            <button id="scanRepoButton" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition-colors">
              <i class="fas fa-search mr-2"></i> Scan Project
            </button>
          </div>
        </div>
        
        <!-- File Upload -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 class="text-xl font-semibold mb-4 flex items-center">
            <i class="fas fa-file-archive text-blue-500 mr-2"></i>
            Upload Archive
          </h3>
          <div class="space-y-4">
            <div>
              <label for="fileUpload" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Project Archive (.zip or .tar)
              </label>
              <input 
                type="file" 
                id="fileUpload" 
                accept=".zip,.tar,.tar.gz" 
                class="w-full custom-file-input py-1.5 text-gray-700 dark:text-gray-300"
              >
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Supported formats: .zip, .tar, .tar.gz
              </p>
            </div>
            <button id="uploadButton" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md transition-colors">
              <i class="fas fa-upload mr-2"></i> Upload & Scan
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Scanning View -->
    <div id="scanningView" class="max-w-4xl mx-auto hidden">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h3 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-cogs text-blue-500 mr-2"></i>
          Processing Project
        </h3>
        
        <!-- Progress Bar -->
        <div class="mb-4">
          <div class="flex justify-between text-sm mb-1">
            <span id="progressStep">Initializing...</span>
            <span id="progressPercent">0%</span>
          </div>
          <div class="bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div id="progressBar" class="bg-blue-600 progress-bar" style="width: 0%"></div>
          </div>
        </div>
        
        <!-- Terminal Log -->
        <div class="terminal" id="terminal">
          <!-- Log entries will be added here dynamically -->
        </div>
      </div>
    </div>
    
    <!-- Project View -->
    <div id="projectView" class="max-w-5xl mx-auto hidden">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h3 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-project-diagram text-blue-500 mr-2"></i>
          Project Overview
        </h3>
        
        <div class="grid md:grid-cols-3 gap-6">
          <!-- Project Stats -->
          <div class="md:col-span-1 space-y-4">
            <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg">
              <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Project Statistics</h4>
              <ul class="space-y-2">
                <li class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-400">Files:</span>
                  <span id="fileCount" class="font-medium">-</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-400">Languages:</span>
                  <span id="languageCount" class="font-medium">-</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-400">Code Lines:</span>
                  <span id="codeLines" class="font-medium">-</span>
                </li>
                <li class="flex justify-between">
                  <span class="text-gray-600 dark:text-gray-400">Current Coverage:</span>
                  <span id="docCoverage" class="font-medium">-</span>
                </li>
              </ul>
            </div>
            
            <!-- Action buttons -->
            <div class="space-y-3">
              <button id="generateCommentsButton" class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-4 rounded-md transition-colors flex items-center justify-center">
                <i class="fas fa-comment-dots mr-2"></i> Generate Doxygen Comments
              </button>
              <button id="generateDocButton" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-md transition-colors flex items-center justify-center">
                <i class="fas fa-book mr-2"></i> Generate Documentation
              </button>
            </div>
          </div>
          
          <!-- File Tree -->
          <div class="md:col-span-2">
            <h4 class="font-medium text-gray-700 dark:text-gray-300 mb-2">Project Structure</h4>
            <div class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-auto" style="max-height: 400px;">
              <div class="file-tree" id="fileTree">
                <!-- File tree will be populated here -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Comments Generation View -->
    <div id="commentsView" class="max-w-5xl mx-auto hidden">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h3 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-comment-dots text-green-500 mr-2"></i>
          Generating Doxygen Comments
        </h3>
        
        <!-- Progress Bar -->
        <div class="mb-4">
          <div class="flex justify-between text-sm mb-1">
            <span id="commentsProgressStep">Initializing AI models...</span>
            <span id="commentsProgressPercent">0%</span>
          </div>
          <div class="bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div id="commentsProgressBar" class="bg-green-600 progress-bar" style="width: 0%"></div>
          </div>
        </div>
      </div>
      
      <!-- Code Diff View (hidden initially) -->
      <div id="diffView" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hidden">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold flex items-center">
            <i class="fas fa-code text-blue-500 mr-2"></i>
            <span id="currentFileName">File Preview</span>
          </h3>
          <div class="flex items-center space-x-2">
            <select id="fileSelector" class="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-3 py-2 rounded-md">
              <!-- File options will be populated here -->
            </select>
            <button id="downloadCommentsButton" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-md transition-colors flex items-center">
              <i class="fas fa-download mr-2"></i> Download Commented Source
            </button>
          </div>
        </div>
        
        <div class="code-diff">
          <div class="column">
            <h4 class="text-lg font-medium mb-2">Original Code</h4>
            <pre><code id="originalCode" class="language-cpp rounded-md"></code></pre>
          </div>
          <div class="column">
            <h4 class="text-lg font-medium mb-2">Commented Code</h4>
            <pre><code id="commentedCode" class="language-cpp rounded-md"></code></pre>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Documentation Generation View -->
    <div id="docGenView" class="max-w-5xl mx-auto hidden">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
        <h3 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-book text-blue-500 mr-2"></i>
          Generating Documentation
        </h3>
        
        <!-- Progress Bar -->
        <div class="mb-4">
          <div class="flex justify-between text-sm mb-1">
            <span id="docProgressStep">Preparing documentation pipeline...</span>
            <span id="docProgressPercent">0%</span>
          </div>
          <div class="bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div id="docProgressBar" class="bg-blue-600 progress-bar" style="width: 0%"></div>
          </div>
        </div>
        
        <!-- Terminal Log -->
        <div class="terminal" id="docTerminal">
          <!-- Log entries will be added here dynamically -->
        </div>
      </div>
      
      <!-- Documentation Download Section -->
      <div id="docDownloadView" class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hidden">
        <h3 class="text-xl font-semibold mb-6 flex items-center">
          <i class="fas fa-download text-green-500 mr-2"></i>
          Documentation Ready
        </h3>
        
        <div class="grid md:grid-cols-2 gap-6">
          <!-- HTML Documentation -->
          <div class="bg-gray-50 dark:bg-gray-700 p-5 rounded-lg text-center">
            <i class="fas fa-code text-blue-500 text-4xl mb-4"></i>
            <h4 class="font-semibold text-lg mb-2">HTML Documentation</h4>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              Complete Doxygen documentation in HTML format with full navigation and search.
            </p>
            <button id="downloadHtmlButton" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-md transition-colors">
              <i class="fas fa-download mr-2"></i> Download HTML Bundle
            </button>
          </div>
          
          <!-- Markdown Documentation -->
          <div class="bg-gray-50 dark:bg-gray-700 p-5 rounded-lg text-center">
            <i class="fab fa-markdown text-blue-500 text-4xl mb-4"></i>
            <h4 class="font-semibold text-lg mb-2">Markdown Documentation</h4>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              Simplified documentation in Markdown format for easy integration with wikis.
            </p>
            <button id="downloadMdButton" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-md transition-colors">
              <i class="fas fa-download mr-2"></i> Download Markdown Bundle
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>
  
  <footer class="bg-white dark:bg-gray-800 py-6 mt-12">
    <div class="container mx-auto px-4 text-center text-gray-600 dark:text-gray-400">
      <p>© 2025 DoxyGen AI - Automated Documentation Generator</p>
    </div>
  </footer>

  <script id="app-script">
    // DOM Elements
    const views = {
      initial: document.getElementById('initialView'),
      scanning: document.getElementById('scanningView'),
      project: document.getElementById('projectView'),
      comments: document.getElementById('commentsView'),
      diffView: document.getElementById('diffView'),
      docGen: document.getElementById('docGenView'),
      docDownload: document.getElementById('docDownloadView')
    };
    
    const buttons = {
      scanRepo: document.getElementById('scanRepoButton'),
      upload: document.getElementById('uploadButton'),
      reset: document.getElementById('resetButton'),
      generateComments: document.getElementById('generateCommentsButton'),
      generateDoc: document.getElementById('generateDocButton'),
      downloadComments: document.getElementById('downloadCommentsButton'),
      downloadHtml: document.getElementById('downloadHtmlButton'),
      downloadMd: document.getElementById('downloadMdButton')
    };
    
    const inputs = {
      repoUrl: document.getElementById('repoUrl'),
      fileUpload: document.getElementById('fileUpload'),
      fileSelector: document.getElementById('fileSelector'),
      darkModeToggle: document.getElementById('darkModeToggle')
    };
    
    const elements = {
      terminal: document.getElementById('terminal'),
      docTerminal: document.getElementById('docTerminal'),
      progressBar: document.getElementById('progressBar'),
      progressStep: document.getElementById('progressStep'),
      progressPercent: document.getElementById('progressPercent'),
      commentsProgressBar: document.getElementById('commentsProgressBar'),
      commentsProgressStep: document.getElementById('commentsProgressStep'),
      commentsProgressPercent: document.getElementById('commentsProgressPercent'),
      docProgressBar: document.getElementById('docProgressBar'),
      docProgressStep: document.getElementById('docProgressStep'),
      docProgressPercent: document.getElementById('docProgressPercent'),
      fileTree: document.getElementById('fileTree'),
      fileCount: document.getElementById('fileCount'),
      languageCount: document.getElementById('languageCount'),
      codeLines: document.getElementById('codeLines'),
      docCoverage: document.getElementById('docCoverage'),
      originalCode: document.getElementById('originalCode'),
      commentedCode: document.getElementById('commentedCode'),
      currentFileName: document.getElementById('currentFileName')
    };
    
    // Utility functions
    function showView(viewToShow) {
      for (const key in views) {
        if (views[key] === viewToShow) {
          views[key].classList.remove('hidden');
          views[key].classList.add('fade-in');
        } else {
          views[key].classList.add('hidden');
          views[key].classList.remove('fade-in');
        }
      }
    }
    
    function setProgress(bar, step, percent, value, message) {
      bar.style.width = `${value}%`;
      step.textContent = message || 'Processing...';
      percent.textContent = `${value}%`;
    }
    
    function logToTerminal(terminal, message, type = 'info') {
      const entry = document.createElement('div');
      entry.className = 'terminal-entry';
      
      const timestamp = document.createElement('span');
      timestamp.className = 'timestamp';
      timestamp.textContent = new Date().toLocaleTimeString();
      
      const text = document.createElement('span');
      text.className = `message ${type}`;
      text.textContent = message;
      
      entry.appendChild(timestamp);
      entry.appendChild(text);
      terminal.appendChild(entry);
      terminal.scrollTop = terminal.scrollHeight;
    }
    
    function simulateProcessWithProgress(progressBar, progressStep, progressPercent, steps, onComplete) {
      let currentStep = 0;
      const totalSteps = steps.length;
      
      function processNextStep() {
        if (currentStep < totalSteps) {
          const step = steps[currentStep];
          const progress = Math.floor((currentStep / totalSteps) * 100);
          
          setProgress(progressBar, progressStep, progressPercent, progress, step.message);
          
          setTimeout(() => {
            if (step.log) {
              for (const log of step.log) {
                logToTerminal(step.terminal || elements.terminal, log.message, log.type || 'info');
              }
            }
            currentStep++;
            processNextStep();
          }, step.duration || 800);
        } else {
          setProgress(progressBar, progressStep, progressPercent, 100, 'Complete!');
          if (onComplete) setTimeout(onComplete, 500);
        }
      }
      
      processNextStep();
    }
    
    // Mock file tree data
    const mockFileTree = {
      name: 'project',
      type: 'folder',
      children: [
        {
          name: 'src',
          type: 'folder',
          children: [
            {
              name: 'main.cpp',
              type: 'file',
              language: 'cpp',
              content: `#include <iostream>\n#include "calculator.h"\n\nint main(int argc, char* argv[]) {\n    Calculator calc;\n    \n    double a = 5.0;\n    double b = 3.0;\n    \n    std::cout << "Sum: " << calc.add(a, b) << std::endl;\n    std::cout << "Difference: " << calc.subtract(a, b) << std::endl;\n    std::cout << "Product: " << calc.multiply(a, b) << std::endl;\n    std::cout << "Quotient: " << calc.divide(a, b) << std::endl;\n    \n    return 0;\n}`
            },
            {
              name: 'calculator.cpp',
              type: 'file',
              language: 'cpp',
              content: `#include "calculator.h"\n\ndouble Calculator::add(double a, double b) {\n    return a + b;\n}\n\ndouble Calculator::subtract(double a, double b) {\n    return a - b;\n}\n\ndouble Calculator::multiply(double a, double b) {\n    return a * b;\n}\n\ndouble Calculator::divide(double a, double b) {\n    if (b == 0) {\n        throw "Division by zero";\n    }\n    return a / b;\n}`
            }
          ]
        },
        {
          name: 'include',
          type: 'folder',
          children: [
            {
              name: 'calculator.h',
              type: 'file',
              language: 'cpp',
              content: `#ifndef CALCULATOR_H\n#define CALCULATOR_H\n\nclass Calculator {\npublic:\n    double add(double a, double b);\n    double subtract(double a, double b);\n    double multiply(double a, double b);\n    double divide(double a, double b);\n};\n\n#endif // CALCULATOR_H`
            }
          ]
        },
        {
          name: 'tests',
          type: 'folder',
          children: [
            {
              name: 'test_calculator.cpp',
              type: 'file',
              language: 'cpp',
              content: `#include <cassert>\n#include "../include/calculator.h"\n\nvoid test_add() {\n    Calculator calc;\n    assert(calc.add(2, 3) == 5);\n    assert(calc.add(-1, 1) == 0);\n    assert(calc.add(0, 0) == 0);\n}\n\nvoid test_subtract() {\n    Calculator calc;\n    assert(calc.subtract(5, 3) == 2);\n    assert(calc.subtract(1, 1) == 0);\n    assert(calc.subtract(0, 5) == -5);\n}\n\nvoid test_multiply() {\n    Calculator calc;\n    assert(calc.multiply(2, 3) == 6);\n    assert(calc.multiply(-1, 5) == -5);\n    assert(calc.multiply(0, 10) == 0);\n}\n\nvoid test_divide() {\n    Calculator calc;\n    assert(calc.divide(6, 3) == 2);\n    assert(calc.divide(5, 2) == 2.5);\n    assert(calc.divide(0, 5) == 0);\n}\n\nint main() {\n    test_add();\n    test_subtract();\n    test_multiply();\n    test_divide();\n    \n    return 0;\n}`
            }
          ]
        },
        {
          name: 'README.md',
          type: 'file',
          language: 'markdown',
          content: `# Calculator Project\n\nA simple calculator library that provides basic arithmetic operations.\n\n## Features\n\n- Addition\n- Subtraction\n- Multiplication\n- Division\n\n## Building\n\n\`\`\`bash\nmkdir build\ncd build\ncmake ..\nmake\n\`\`\``
        }
      ]
    };
    
    // Mock commented file data
    const mockCommentedFiles = {
      'src/main.cpp': `#include <iostream>\n#include "calculator.h"\n\n/**\n * @brief Main entry point of the calculator demo program\n * @param argc Number of command line arguments\n * @param argv Array of command line arguments\n * @return Exit code (0 for success)\n */\nint main(int argc, char* argv[]) {\n    Calculator calc;\n    \n    double a = 5.0;\n    double b = 3.0;\n    \n    std::cout << "Sum: " << calc.add(a, b) << std::endl;\n    std::cout << "Difference: " << calc.subtract(a, b) << std::endl;\n    std::cout << "Product: " << calc.multiply(a, b) << std::endl;\n    std::cout << "Quotient: " << calc.divide(a, b) << std::endl;\n    \n    return 0;\n}`,
      
      'src/calculator.cpp': `#include "calculator.h"\n\n/**\n * @brief Adds two numbers\n * @param a First operand\n * @param b Second operand\n * @return Sum of a and b\n */\ndouble Calculator::add(double a, double b) {\n    return a + b;\n}\n\n/**\n * @brief Subtracts second number from the first\n * @param a First operand\n * @param b Second operand\n * @return Difference between a and b (a-b)\n */\ndouble Calculator::subtract(double a, double b) {\n    return a - b;\n}\n\n/**\n * @brief Multiplies two numbers\n * @param a First operand\n * @param b Second operand\n * @return Product of a and b\n */\ndouble Calculator::multiply(double a, double b) {\n    return a * b;\n}\n\n/**\n * @brief Divides first number by the second\n * @param a First operand (numerator)\n * @param b Second operand (denominator)\n * @return Quotient of a and b (a/b)\n * @throw const char* Exception if b is zero\n */\ndouble Calculator::divide(double a, double b) {\n    if (b == 0) {\n        throw "Division by zero";\n    }\n    return a / b;\n}`,
      
      'include/calculator.h': `#ifndef CALCULATOR_H\n#define CALCULATOR_H\n\n/**\n * @class Calculator\n * @brief A simple calculator class providing basic arithmetic operations\n * \n * This class implements four basic arithmetic operations: addition, subtraction,\n * multiplication and division for double precision floating point numbers.\n */\nclass Calculator {\npublic:\n    /**\n     * @brief Adds two numbers\n     * @param a First operand\n     * @param b Second operand\n     * @return Sum of a and b\n     */\n    double add(double a, double b);\n    \n    /**\n     * @brief Subtracts second number from the first\n     * @param a First operand\n     * @param b Second operand\n     * @return Difference between a and b (a-b)\n     */\n    double subtract(double a, double b);\n    \n    /**\n     * @brief Multiplies two numbers\n     * @param a First operand\n     * @param b Second operand\n     * @return Product of a and b\n     */\n    double multiply(double a, double b);\n    \n    /**\n     * @brief Divides first number by the second\n     * @param a First operand (numerator)\n     * @param b Second operand (denominator)\n     * @return Quotient of a and b (a/b)\n     * @throw const char* Exception if b is zero\n     */\n    double divide(double a, double b);\n};\n\n#endif // CALCULATOR_H`,
      
      'tests/test_calculator.cpp': `#include <cassert>\n#include "../include/calculator.h"\n\n/**\n * @brief Tests the Calculator::add method\n * @details Verifies addition with positive, negative and zero values\n */\nvoid test_add() {\n    Calculator calc;\n    assert(calc.add(2, 3) == 5);\n    assert(calc.add(-1, 1) == 0);\n    assert(calc.add(0, 0) == 0);\n}\n\n/**\n * @brief Tests the Calculator::subtract method\n * @details Verifies subtraction with positive, negative and zero values\n */\nvoid test_subtract() {\n    Calculator calc;\n    assert(calc.subtract(5, 3) == 2);\n    assert(calc.subtract(1, 1) == 0);\n    assert(calc.subtract(0, 5) == -5);\n}\n\n/**\n * @brief Tests the Calculator::multiply method\n * @details Verifies multiplication with positive, negative and zero values\n */\nvoid test_multiply() {\n    Calculator calc;\n    assert(calc.multiply(2, 3) == 6);\n    assert(calc.multiply(-1, 5) == -5);\n    assert(calc.multiply(0, 10) == 0);\n}\n\n/**\n * @brief Tests the Calculator::divide method\n * @details Verifies division with various inputs, including division by zero\n */\nvoid test_divide() {\n    Calculator calc;\n    assert(calc.divide(6, 3) == 2);\n    assert(calc.divide(5, 2) == 2.5);\n    assert(calc.divide(0, 5) == 0);\n}\n\n/**\n * @brief Main test function that runs all calculator tests\n * @return 0 if all tests pass\n */\nint main() {\n    test_add();\n    test_subtract();\n    test_multiply();\n    test_divide();\n    \n    return 0;\n}`
    };
    
    // Function to build file tree UI
    function buildFileTreeUI(tree, container) {
      const ul = document.createElement('ul');
      
      tree.children.forEach(item => {
        const li = document.createElement('li');
        
        if (item.type === 'folder') {
          li.innerHTML = `<i class="fas fa-folder mr-2 folder"></i> ${item.name}`;
          li.classList.add('folder');
          const nestedUl = buildFileTreeUI(item, li);
          li.appendChild(nestedUl);
        } else {
          li.innerHTML = `<i class="fas fa-file-code mr-2 file"></i> ${item.name}`;
          li.classList.add('file');
          li.addEventListener('click', () => {
            displayFileContent(item);
          });
        }
        
        ul.appendChild(li);
      });
      
      return ul;
    }
    
    function displayFileContent(file) {
      // This would typically open a modal or other view to show the file
      console.log("File clicked:", file.name);
      // Implementation for preview would go here
    }
    
    // Function to find file path in tree
    function findFilePath(tree, fileName, currentPath = '') {
      for (const item of tree.children || []) {
        const newPath = currentPath ? `${currentPath}/${item.name}` : item.name;
        
        if (item.type === 'file' && item.name === fileName) {
          return newPath;
        }
        
        if (item.type === 'folder') {
          const result = findFilePath(item, fileName, newPath);
          if (result) return result;
        }
      }
      
      return null;
    }
    
    // Function to get file by path
    function getFileByPath(tree, path) {
      const parts = path.split('/');
      let current = tree;
      
      for (let i = 0; i < parts.length; i++) {
        const part = parts[i];
        if (i === 0 && part === current.name) continue;
        
        let found = false;
        for (const child of current.children || []) {
          if (child.name === part) {
            current = child;
            found = true;
            break;
          }
        }
        
        if (!found) return null;
      }
      
      return current;
    }
    
    // Function to get all file paths in tree
    function getAllFilePaths(tree, currentPath = '') {
      let paths = [];
      
      for (const item of tree.children || []) {
        const newPath = currentPath ? `${currentPath}/${item.name}` : item.name;
        
        if (item.type === 'file') {
          paths.push(newPath);
        }
        
        if (item.type === 'folder') {
          paths = paths.concat(getAllFilePaths(item, newPath));
        }
      }
      
      return paths;
    }
    
    // Function to populate file selector dropdown
    function populateFileSelector(fileTree) {
      const paths = getAllFilePaths(fileTree);
      inputs.fileSelector.innerHTML = '';
      
      paths.forEach(path => {
        if (path.endsWith('.cpp') || path.endsWith('.h')) {
          const option = document.createElement('option');
          option.value = path;
          option.textContent = path;
          inputs.fileSelector.appendChild(option);
        }
      });
    }
    
    // Function to update diff view with selected file
    function updateDiffView(filePath) {
      elements.currentFileName.textContent = filePath;
      
      // Get original content
      const file = getFileByPath(mockFileTree, filePath);
      if (file) {
        elements.originalCode.textContent = file.content;
        elements.originalCode.className = `language-${file.language || 'cpp'}`;
        hljs.highlightElement(elements.originalCode);
      }
      
      // Get commented content
      const commentedContent = mockCommentedFiles[filePath];
      if (commentedContent) {
        elements.commentedCode.textContent = commentedContent;
        elements.commentedCode.className = `language-${file.language || 'cpp'}`;
        hljs.highlightElement(elements.commentedCode);
      }
    }
    
    // Initialize the application
    function initApp() {
      // Set up dark mode toggle
      const prefersDarkScheme = window.matchMedia("(prefers-color-scheme: dark)");
      if (prefersDarkScheme.matches) {
        document.body.classList.add("dark");
        inputs.darkModeToggle.checked = true;
      } else {
        document.body.classList.remove("dark");
        inputs.darkModeToggle.checked = false;
      }
      
      inputs.darkModeToggle.addEventListener("change", () => {
        if (inputs.darkModeToggle.checked) {
          document.body.classList.add("dark");
        } else {
          document.body.classList.remove("dark");
        }
      });
      
      // Set up button event handlers
      buttons.scanRepo.addEventListener('click', handleRepoScan);
      buttons.upload.addEventListener('click', handleFileUpload);
      buttons.reset.addEventListener('click', resetApp);
      buttons.generateComments.addEventListener('click', handleGenerateComments);
      buttons.generateDoc.addEventListener('click', handleGenerateDoc);
      buttons.downloadComments.addEventListener('click', handleDownloadComments);
      buttons.downloadHtml.addEventListener('click', handleDownloadHtml);
      buttons.downloadMd.addEventListener('click', handleDownloadMd);
      
      inputs.fileSelector.addEventListener('change', () => {
        const selectedPath = inputs.fileSelector.value;
        updateDiffView(selectedPath);
      });
      
      // Show initial view
      showView(views.initial);
    }
    
    // Handler functions
    function handleRepoScan() {
      const repoUrl = inputs.repoUrl.value.trim();
      
      if (!repoUrl) {
        alert('Please enter a repository URL');
        return;
      }
      
      showView(views.scanning);
      
      const scanSteps = [
        {
          message: 'Cloning repository...',
          duration: 1500,
          log: [
            { message: 'Initializing git clone operation' },
            { message: `Cloning from ${repoUrl}` }
          ]
        },
        {
          message: 'Detecting languages...',
          duration: 800,
          log: [
            { message: 'Analyzing file extensions' },
            { message: 'Detected languages: C++, CMake, Markdown' }
          ]
        },
        {
          message: 'Indexing files...',
          duration: 1200,
          log: [
            { message: 'Scanning directory structure' },
            { message: 'Found 4 source files (.cpp)' },
            { message: 'Found 1 header file (.h)' },
            { message: 'Found 1 markdown file (.md)' },
            { message: 'Indexing complete', type: 'success' }
          ]
        },
        {
          message: 'Analyzing code structure...',
          duration: 1000,
          log: [
            { message: 'Parsing C++ files' },
            { message: 'Extracting classes and functions' },
            { message: 'Analysis complete', type: 'success' }
          ]
        }
      ];
      
      simulateProcessWithProgress(
        elements.progressBar, 
        elements.progressStep, 
        elements.progressPercent, 
        scanSteps,
        () => {
          showProjectView();
        }
      );
    }
    
    function handleFileUpload() {
      const fileInput = inputs.fileUpload;
      
      if (!fileInput.files || fileInput.files.length === 0) {
        alert('Please select a file to upload');
        return;
      }
      
      const file = fileInput.files[0];
      showView(views.scanning);
      
      const scanSteps = [
        {
          message: 'Uploading file...',
          duration: 1000,
          log: [
            { message: `Uploading ${file.name} (${(file.size / 1024).toFixed(2)} KB)` }
          ]
        },
        {
          message: 'Extracting archive...',
          duration: 1200,
          log: [
            { message: 'Extracting files from archive' },
            { message: 'Validating archive contents' }
          ]
        },
        {
          message: 'Detecting languages...',
          duration: 800,
          log: [
            { message: 'Analyzing file extensions' },
            { message: 'Detected languages: C++, CMake, Markdown' }
          ]
        },
        {
          message: 'Indexing files...',
          duration: 1200,
          log: [
            { message: 'Scanning directory structure' },
            { message: 'Found 4 source files (.cpp)' },
            { message: 'Found 1 header file (.h)' },
            { message: 'Found 1 markdown file (.md)' },
            { message: 'Indexing complete', type: 'success' }
          ]
        },
        {
          message: 'Analyzing code structure...',
          duration: 1000,
          log: [
            { message: 'Parsing C++ files' },
            { message: 'Extracting classes and functions' },
            { message: 'Analysis complete', type: 'success' }
          ]
        }
      ];
      
      simulateProcessWithProgress(
        elements.progressBar, 
        elements.progressStep, 
        elements.progressPercent, 
        scanSteps,
        () => {
          showProjectView();
        }
      );
    }
    
    function showProjectView() {
      // Populate project stats
      elements.fileCount.textContent = '6';
      elements.languageCount.textContent = '2 (C++, Markdown)';
      elements.codeLines.textContent = '142';
      elements.docCoverage.textContent = '12% (needs improvement)';
      
      // Build file tree
      elements.fileTree.innerHTML = '';
      const treeUI = buildFileTreeUI(mockFileTree, elements.fileTree);
      elements.fileTree.appendChild(treeUI);
      
      // Show project view
      showView(views.project);
    }
    
    function handleGenerateComments() {
      showView(views.comments);
      
      const commentSteps = [
        {
          message: 'Initializing AI models...',
          duration: 800
        },
        {
          message: 'Analyzing code structure...',
          duration: 1200
        },
        {
          message: 'Generating comments for header files...',
          duration: 1500
        },
        {
          message: 'Generating comments for implementation files...',
          duration: 2000
        },
        {
          message: 'Finalizing documentation...',
          duration: 1000
        }
      ];
      
      simulateProcessWithProgress(
        elements.commentsProgressBar, 
        elements.commentsProgressStep, 
        elements.commentsProgressPercent, 
        commentSteps,
        () => {
          // Show diff view with file comparison
          views.diffView.classList.remove('hidden');
          views.diffView.classList.add('fade-in');
          
          // Populate file selector
          populateFileSelector(mockFileTree);
          
          // Set initial file in diff view
          if (inputs.fileSelector.options.length > 0) {
            const initialFilePath = inputs.fileSelector.options[0].value;
            updateDiffView(initialFilePath);
          }
        }
      );
    }
    
    function handleGenerateDoc() {
      showView(views.docGen);
      
      const docSteps = [
        {
          message: 'Configuring Doxygen...',
          duration: 800,
          log: [
            { message: 'Creating Doxyfile configuration' },
            { message: 'Setting output directories' }
          ],
          terminal: elements.docTerminal
        },
        {
          message: 'Running Doxygen...',
          duration: 1500,
          log: [
            { message: 'Processing header files' },
            { message: 'Processing implementation files' },
            { message: 'Generating class hierarchy' },
            { message: 'Creating HTML output' }
          ],
          terminal: elements.docTerminal
        },
        {
          message: 'Creating Markdown output...',
          duration: 1200,
          log: [
            { message: 'Converting HTML to Markdown' },
            { message: 'Creating table of contents' },
            { message: 'Formatting code examples' }
          ],
          terminal: elements.docTerminal
        },
        {
          message: 'Finalizing documentation...',
          duration: 1000,
          log: [
            { message: 'Compressing HTML bundle' },
            { message: 'Compressing Markdown bundle' },
            { message: 'Documentation generation complete!', type: 'success' }
          ],
          terminal: elements.docTerminal
        }
      ];
      
      simulateProcessWithProgress(
        elements.docProgressBar, 
        elements.docProgressStep, 
        elements.docProgressPercent, 
        docSteps,
        () => {
          // Show download view
          views.docDownloadView.classList.remove('hidden');
          views.docDownloadView.classList.add('fade-in');
        }
      );
    }
    
    function handleDownloadComments() {
      // In a real implementation, this would create a downloadable ZIP file
      alert('This is a prototype. In the full version, this would download the commented source code as a ZIP file.');
    }
    
    function handleDownloadHtml() {
      // In a real implementation, this would create a downloadable ZIP file with HTML docs
      alert('This is a prototype. In the full version, this would download the HTML documentation bundle.');
    }
    
    function handleDownloadMd() {
      // In a real implementation, this would create a downloadable ZIP file with Markdown docs
      alert('This is a prototype. In the full version, this would download the Markdown documentation bundle.');
    }
    
    function resetApp() {
      // Reset all form fields
      inputs.repoUrl.value = '';
      inputs.fileUpload.value = '';
      
      // Reset terminals
      elements.terminal.innerHTML = '';
      elements.docTerminal.innerHTML = '';
      
      // Show initial view
      showView(views.initial);
    }
    
    // Initialize the application when the page loads
    document.addEventListener('DOMContentLoaded', initApp);
  </script>
</body>
</html>