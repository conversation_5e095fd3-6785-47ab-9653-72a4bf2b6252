# Code Documentation Generator

An AI-powered tool that automatically generates comprehensive documentation for Git repositories.

## Features

- Automatic code documentation generation
- Git repository parsing and analysis
- Secure authentication system
- Detailed inline code comments
- High-level repository documentation

## Setup

1. Create a virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Linux/Mac
# or
.\venv\Scripts\activate  # On Windows
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Create a `.env` file with your configuration:

```bash
cp .env.example .env
# Edit .env with your settings
```

4. Run the development server:

```bash
python main.py
```

## Project Structure

- `src/` - Main source code directory
  - `auth/` - Authentication related code
  - `ml/` - Machine learning models and utilities
  - `git/` - Git repository handling
  - `docs/` - Documentation generation logic
- `tests/` - Test files
- `config/` - Configuration files
- `main.py` - Application entry point

## Usage

[Documentation will be added as the project develops]
