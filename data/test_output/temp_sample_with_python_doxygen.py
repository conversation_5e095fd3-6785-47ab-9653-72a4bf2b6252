##
# @file temp_sample_with_python_doxygen.py
# @brief Script to process text files by converting contents to uppercase.
##

import os
import sys
from typing import Dict

##
# @class DataProcessor
# @brief Handles processing of files from an input directory and saves them to an output directory.
##
class DataProcessor:
    ##
    # @brief Constructor.
    # @param input_dir Path to the input directory containing text files.
    # @param output_dir Path to the output directory for processed files.
    ##
    def __init__(self, input_dir: str, output_dir: str):
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.processed_files = 0

    ##
    # @brief Processes a single file.
    # @param filename Name of the file to process.
    # @return True if processing was successful, False otherwise.
    ##
    def process_file(self, filename: str) -> bool:
        try:
            input_path = os.path.join(self.input_dir, filename)
            output_path = os.path.join(self.output_dir, f"processed_{filename}")
            with open(input_path, 'r') as f:
                content = f.read()
            processed_content = content.upper()
            with open(output_path, 'w') as f:
                f.write(processed_content)
            self.processed_files += 1
            return True
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            return False

    ##
    # @brief Processes all files in the input directory.
    # @return A dictionary containing counts of successful and failed file processes.
    ##
    def process_all_files(self) -> Dict[str, int]:
        results = {"success": 0, "failed": 0}
        for filename in os.listdir(self.input_dir):
            if os.path.isfile(os.path.join(self.input_dir, filename)):
                success = self.process_file(filename)
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1
        return results

##
# @brief Main entry point. Parses arguments and runs the processing logic.
##
def main():
    if len(sys.argv) != 3:
        print("Usage: python script.py input_dir output_dir")
        return
    input_dir = sys.argv[1]
    output_dir = sys.argv[2]
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    processor = DataProcessor(input_dir, output_dir)
    results = processor.process_all_files()
    print(f"Processing complete. Success: {results['success']}, Failed: {results['failed']}")

if __name__ == "__main__":
    main()
