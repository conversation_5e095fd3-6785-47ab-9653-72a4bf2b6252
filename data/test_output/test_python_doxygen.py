#!/usr/bin/env python3
"""!
@file test_python_doxygen.py
@brief Test file for Python Doxygen comments
"""

"""!
@brief A simple class for testing Doxygen comments
"""
class Calculator:
    """!
    @brief Initialize the calculator
    """
    def __init__(self):
        self.result = 0

    """!
    @brief Add two numbers
    @param a First number
    @param b Second number
    @return Sum of a and b
    """
    def add(self, a, b):
        return a + b

    """!
    @brief Subtract two numbers
    @param a First number
    @param b Second number
    @return Difference between a and b
    """
    def subtract(self, a, b):
        return a - b
