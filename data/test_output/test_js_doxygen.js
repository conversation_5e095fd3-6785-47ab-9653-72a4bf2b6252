/**
 * @file test_js_doxygen.js
 * @brief Test file for JavaScript Doxygen comments
 */

/**
 * @brief A simple class for testing Doxygen comments
 */
class Calculator {
    /**
     * @brief Initialize the calculator
     */
    constructor() {
        this.result = 0;
    }

    /**
     * @brief Add two numbers
     * @param {number} a First number
     * @param {number} b Second number
     * @return {number} Sum of a and b
     */
    add(a, b) {
        return a + b;
    }

    /**
     * @brief Subtract two numbers
     * @param {number} a First number
     * @param {number} b Second number
     * @return {number} Difference between a and b
     */
    subtract(a, b) {
        return a - b;
    }
}
