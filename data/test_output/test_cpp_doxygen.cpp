/**
 * @file test_cpp_doxygen.cpp
 * @brief Test file for C++ Doxygen comments
 */

/**
 * @brief A simple class for testing Doxygen comments
 */
class Calculator {
public:
    /**
     * @brief Initialize the calculator
     */
    Calculator() {
        result = 0;
    }

    /**
     * @brief Add two numbers
     * @param a First number
     * @param b Second number
     * @return Sum of a and b
     */
    int add(int a, int b) {
        return a + b;
    }

    /**
     * @brief Subtract two numbers
     * @param a First number
     * @param b Second number
     * @return Difference between a and b
     */
    int subtract(int a, int b) {
        return a - b;
    }

private:
    int result;
};
