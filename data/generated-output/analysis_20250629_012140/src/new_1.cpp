#include <ros/ros.h>
#include <teb_local_planner/teb_local_planner_ros.h>
#include <visualization_msgs/Marker.h>
#include <boost/shared_ptr.hpp>
#include <boost/make_shared.hpp>
#include <geometry_msgs/PoseStamped.h>
#include <costmap_converter/ObstacleArrayMsg.h>
#include <nav_msgs/Path.h>
#include "teb_planner_service/PlanTrajectory.h"

/**
 * @file teb_service.cpp
 * @brief ROS service node for trajectory planning using the TEB local planner.
 *
 * This node provides a ROS service that computes a trajectory using the Timed Elastic Band (TEB) local planner.
 * It receives start and goal poses, as well as obstacles, and returns a planned trajectory as a nav_msgs/Path.
 *
 * <AUTHOR> Name)
 * @date (Date)
 */

using namespace teb_local_planner;

/**
 * @brief Global pointer to the planner interface (TEB or Homotopy).
 */
PlannerInterfacePtr planner;
/**
 * @brief Global pointer to the TEB visualization object.
 */
TebVisualizationPtr visual;
/**
 * @brief Global container for obstacle pointers.
 */
std::vector<ObstaclePtr> obst_vector;
/**
 * @brief Global container for via points.
 */
ViaPointContainer via_points;
/**
 * @brief Global configuration object for the TEB planner.
 */
TebConfig config;
/**
 * @brief Dynamic reconfigure server for TEB planner parameters.
 */
boost::shared_ptr<dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>> dynamic_recfg;
/**
 * @brief ROS subscriber for custom obstacles (not used in this file).
 */
ros::Subscriber custom_obst_sub;
/**
 * @brief ROS service server for trajectory planning.
 */
ros::ServiceServer teb_service;

void CB_publishCycle(const ros::TimerEvent &e);
void CB_reconfigure(TebLocalPlannerReconfigureConfig &reconfig, uint32_t level);
bool planTrajectory(teb_planner_service::PlanTrajectory::Request &req, teb_planner_service::PlanTrajectory::Response &res);

/**
 * @brief Main entry point for the TEB planner service node.
 *
 * Initializes ROS, loads parameters, sets up dynamic reconfigure, visualization, and the service server.
 *
 * @param argc Number of command-line arguments.
 * @param argv Array of command-line arguments.
 * @return int Exit status code.
 */
int main(int argc, char **argv)
{
    ros::init(argc, argv, "test_teb_service");
    ros::NodeHandle n("~");

    config.loadRosParamFromNodeHandle(n);

    // Setup dynamic reconfigure
    dynamic_recfg = boost::make_shared<dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>>(n);
    dynamic_reconfigure::Server<TebLocalPlannerReconfigureConfig>::CallbackType cb = boost::bind(CB_reconfigure, _1, _2);
/**
 * Convert from ROS message format to the planner's internal obstacle representation. Handles both point and polygon obstacles.
 */
    dynamic_recfg->setCallback(cb);

    // Setup visualization
    visual = TebVisualizationPtr(new TebVisualization(n, config));

    // Setup robot shape model
    config.robot_model = TebLocalPlannerROS::getRobotFootprintFromParamServer(n, config);

    // std::cout<<"service is ready !!"<<std::endl;
    teb_service = n.advertiseService("plan_trajectory", planTrajectory);
    ROS_INFO("TEB Planner Service Ready!");

    // ros::Timer publish_timer = n.createTimer(ros::Duration(0.1), CB_publishCycle);

    ros::spin();
    return 0;
/**
 * Selects between TEB and Homotopy Class Planner based on configuration.
 */
}

/**
 * @brief Service callback to plan a trajectory using the TEB local planner.
 *
 * Converts obstacles, selects the planner type, plans the trajectory, and fills the response with the computed path.
/**
 * Converts from ROS PoseStamped message to the planner's internal PoseSE2 representation.
 */
 *
 * @param req Service request containing current pose, goal pose, and obstacles.
 * @param res Service response containing the planned trajectory as nav_msgs/Path.
 * @return true if planning was successful, false otherwise.
 */
bool planTrajectory(teb_planner_service::PlanTrajectory::Request &req,
                    teb_planner_service::PlanTrajectory::Response &res)
{
    ROS_INFO("Received request for trajectory planning.");

/**
 * Handles the case where the planner might be either TebOptimalPlanner or HomotopyClassPlanner, extracting the optimal trajectory.
 */
    // Convert obstacles from message to TEB format
/**
 * Uses dynamic_pointer_cast for safe downcasting to access specific planner methods.
 */
    obst_vector.clear();
    for (const auto &obst : req.obstacles.obstacles)
    {
        if (obst.polygon.points.size() == 1)
        {
            obst_vector.push_back(ObstaclePtr(new PointObstacle(obst.polygon.points[0].x, obst.polygon.points[0].y)));
        }
/**
 * Handles potential failure to retrieve the optimal trajectory from a HomotopyClassPlanner.
 */
        else
        {
            PolygonObstacle *poly_obst = new PolygonObstacle;
            for (const auto &point : obst.polygon.points)
            {
                poly_obst->pushBackVertex(point.x, point.y);
            }
            poly_obst->finalizePolygon();
            obst_vector.push_back(ObstaclePtr(poly_obst));
        }
    }

/**
 * Optionally visualizes time as the z-axis for better trajectory understanding in RViz.
 */
    // Select planner type
    if (config.hcp.enable_homotopy_class_planning)
        planner = PlannerInterfacePtr(new HomotopyClassPlanner(config, &obst_vector, visual, &via_points));
    else
        planner = PlannerInterfacePtr(new TebOptimalPlanner(config, &obst_vector, visual, &via_points));

    // Convert current pose and goal pose to PoseSE2
    PoseSE2 start(req.current_pose.pose.position.x, req.current_pose.pose.position.y, tf::getYaw(req.current_pose.pose.orientation));
    PoseSE2 goal(req.goal_pose.pose.position.x, req.goal_pose.pose.position.y, tf::getYaw(req.goal_pose.pose.orientation));

    // Plan trajectory
    if (!planner->plan(start, goal))
    {
        ROS_WARN("TEB Planner failed to compute a trajectory!");
        return false;
    }
/**
 * Periodically publishes visualization markers for debugging and monitoring.
 */

    // Get best trajectory
    TebOptimalPlannerPtr planner_optimal = boost::dynamic_pointer_cast<TebOptimalPlanner>(planner);
    if (!planner_optimal)
    {
        HomotopyClassPlannerPtr planner_homotopy = boost::dynamic_pointer_cast<HomotopyClassPlanner>(planner);
        if (planner_homotopy)
            planner_optimal = planner_homotopy->bestTeb();
    }

/**
 * Allows for runtime modification of planner parameters without restarting the node.
 */
    if (!planner_optimal)
    {
        ROS_ERROR("Failed to retrieve the optimal trajectory from the planner!");
        return false;
    }

    nav_msgs::Path teb_path;
    teb_path.header.frame_id = config.map_frame;
    teb_path.header.stamp = ros::Time::now();

    for (int i = 0; i < planner_optimal->teb().sizePoses(); i++)
    {
        geometry_msgs::PoseStamped pose;
        pose.header = teb_path.header;
        pose.pose.position.x = planner_optimal->teb().Pose(i).x();
        pose.pose.position.y = planner_optimal->teb().Pose(i).y();
        pose.pose.position.z = config.hcp.visualize_with_time_as_z_axis_scale * planner_optimal->teb().getSumOfTimeDiffsUpToIdx(i);
        pose.pose.orientation = tf::createQuaternionMsgFromYaw(planner_optimal->teb().Pose(i).theta());
        teb_path.poses.push_back(pose);
    }

    res.trajectory = teb_path;
    ROS_INFO("Trajectory successfully planned and sent.");
    return true;
}

/**
 * @brief Timer callback for publishing visualization markers.
 *
 * Publishes the current planner state, obstacles, and via points for visualization in RViz.
 *
 * @param e Timer event information (unused).
 */
void CB_publishCycle(const ros::TimerEvent &e)
{
    if (planner)
    {
        planner->visualize();
        visual->publishObstacles(obst_vector);
        visual->publishViaPoints(via_points);
    }
}

/**
 * @brief Dynamic reconfigure callback for updating TEB planner parameters at runtime.
 *
 * @param reconfig New configuration parameters.
 * @param level Dynamic reconfigure level (unused).
 */
void CB_reconfigure(TebLocalPlannerReconfigureConfig &reconfig, uint32_t level)
{
    config.reconfigure(reconfig);
}
