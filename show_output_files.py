#!/usr/bin/env python3
"""
Script to show where commented files are saved and display their contents.
"""

import os
import glob
from datetime import datetime

def find_output_directories():
    """Find all analysis output directories."""
    output_base = "data/generated-output"
    if not os.path.exists(output_base):
        print(f"❌ Output directory not found: {output_base}")
        return []
    
    # Find all analysis_* directories
    pattern = os.path.join(output_base, "analysis_*")
    directories = glob.glob(pattern)
    directories.sort(reverse=True)  # Most recent first
    
    return directories

def show_directory_contents(directory):
    """Show contents of an analysis directory."""
    print(f"\n📁 Directory: {directory}")
    print(f"📅 Created: {datetime.fromtimestamp(os.path.getctime(directory))}")
    
    # Find all files in the directory
    files = []
    for root, dirs, filenames in os.walk(directory):
        for filename in filenames:
            file_path = os.path.join(root, filename)
            rel_path = os.path.relpath(file_path, directory)
            files.append((rel_path, file_path))
    
    if not files:
        print("   📄 No files found")
        return
    
    print(f"   📄 Files ({len(files)} total):")
    for rel_path, full_path in files:
        file_size = os.path.getsize(full_path)
        print(f"      {rel_path} ({file_size} bytes)")

def show_file_with_comments(file_path, max_lines=50):
    """Show a file with its generated comments."""
    print(f"\n📄 File: {file_path}")
    print("=" * 60)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 Total lines: {len(lines)}")
        
        # Show first max_lines lines
        for i, line in enumerate(lines[:max_lines], 1):
            # Highlight comment lines
            line = line.rstrip()
            if any(comment_marker in line for comment_marker in ['/**', '*/', '///', '"""!', '##!']):
                print(f"{i:3d}: 💬 {line}")
            else:
                print(f"{i:3d}:    {line}")
        
        if len(lines) > max_lines:
            print(f"... ({len(lines) - max_lines} more lines)")
            
    except Exception as e:
        print(f"❌ Error reading file: {e}")

def main():
    """Main function to show output files."""
    print("🔍 AI AGENT OUTPUT FILES EXPLORER")
    print("=" * 50)
    
    # Find output directories
    directories = find_output_directories()
    
    if not directories:
        print("❌ No output directories found.")
        print("💡 Run the main application first:")
        print("   ./activate_ai_agent.sh")
        print("   python app/main.py --local-dir")
        return
    
    print(f"📁 Found {len(directories)} output directories:")
    
    # Show all directories
    for i, directory in enumerate(directories, 1):
        print(f"\n{i}. {os.path.basename(directory)}")
        show_directory_contents(directory)
    
    # Show the most recent directory's files in detail
    if directories:
        latest_dir = directories[0]
        print(f"\n🔍 DETAILED VIEW OF LATEST DIRECTORY:")
        print(f"📁 {latest_dir}")
        
        # Find Python files to show as examples
        python_files = []
        for root, dirs, filenames in os.walk(latest_dir):
            for filename in filenames:
                if filename.endswith('.py'):
                    file_path = os.path.join(root, filename)
                    python_files.append(file_path)
        
        if python_files:
            print(f"\n📄 EXAMPLE: First Python file with comments")
            show_file_with_comments(python_files[0])
        else:
            # Show any file as example
            all_files = []
            for root, dirs, filenames in os.walk(latest_dir):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    all_files.append(file_path)
            
            if all_files:
                print(f"\n📄 EXAMPLE: First file with comments")
                show_file_with_comments(all_files[0])
    
    print(f"\n💡 To see more files, navigate to the directories shown above")
    print(f"💡 Files with 💬 markers contain generated comments")

if __name__ == "__main__":
    main()
