#!/usr/bin/env python3
"""
Enhanced test script to show detailed LLM logging - exactly what's sent and received.
"""

import asyncio
import os
import sys
import json
import logging
from dotenv import load_dotenv

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.llm import get_llm_generator

# Load environment variables
load_dotenv()

# Set up detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('llm_detailed_logs.log')
    ]
)

# Simple test code for comment generation
TEST_CODE = '''def calculate_fibonacci(n):
    """Calculate the nth Fibonacci number using recursion."""
    if n <= 1:
        return n
    else:
        return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

def process_data(data_list):
    """Process a list of data items with complex logic."""
    result = []
    for item in data_list:
        if isinstance(item, dict) and 'value' in item:
            processed_value = item['value'] * 2 + 1
            result.append({'processed': processed_value, 'original': item})
    return result

class DataProcessor:
    """A class for processing various types of data."""
    
    def __init__(self, config):
        self.config = config
        self.cache = {}
    
    def process(self, input_data):
        """Main processing method with caching."""
        cache_key = str(hash(str(input_data)))
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        result = self._internal_process(input_data)
        self.cache[cache_key] = result
        return result
    
    def _internal_process(self, data):
        """Internal processing logic."""
        return data.upper() if isinstance(data, str) else str(data)
'''

async def test_provider_detailed(provider_name):
    """Test a specific LLM provider with detailed logging."""
    print(f"\n{'='*60}")
    print(f"🧪 DETAILED TEST: {provider_name.upper()} PROVIDER")
    print(f"{'='*60}")
    
    try:
        # Get the LLM generator
        print(f"🔧 Initializing {provider_name} generator...")
        generator = get_llm_generator(provider_name)
        print(f"✅ Successfully initialized {provider_name} generator")
        
        # Test comment generation with detailed logging
        print(f"\n🚀 Starting comment generation with {provider_name}...")
        print(f"📊 Input code stats:")
        print(f"   - Lines: {len(TEST_CODE.split(chr(10)))}")
        print(f"   - Characters: {len(TEST_CODE)}")
        print(f"   - Language: Python")
        
        print(f"\n📤 Sending request to {provider_name}...")
        comments = await generator.generate_inline_comments(TEST_CODE, "python")
        
        print(f"\n📥 Response received from {provider_name}")
        if comments:
            print(f"✅ Successfully generated {len(comments)} comments")
            print(f"\n📋 Generated Comments Summary:")
            for i, comment in enumerate(comments, 1):
                line_num = comment.get('line_number', 'N/A')
                comment_text = comment.get('comment', 'N/A')
                # Truncate long comments for display
                display_comment = comment_text[:80] + "..." if len(comment_text) > 80 else comment_text
                print(f"   {i:2d}. Line {line_num:2s}: {display_comment}")
            
            # Show full JSON structure
            print(f"\n📄 Full JSON Response:")
            print(json.dumps(comments, indent=2))
            
        else:
            print(f"⚠️  No comments generated by {provider_name}")
            
        return True, comments
        
    except Exception as e:
        print(f"❌ Error testing {provider_name}: {e}")
        return False, None

async def main():
    """Main test function with detailed logging."""
    print("🚀 DETAILED LLM LOGGING TEST")
    print("=" * 60)
    print("This test will show exactly what's sent to and received from LLMs")
    print("Check 'llm_detailed_logs.log' for complete debug logs")
    print("=" * 60)
    
    # Check available providers
    available_providers = []
    
    if os.getenv("GROQ_API_KEY"):
        available_providers.append("groq")
    if os.getenv("OPENAI_API_KEY"):
        available_providers.append("openai")
    if os.getenv("GOOGLE_GEMINI_API_KEY"):
        available_providers.append("gemini")
    
    if not available_providers:
        print("❌ No API keys found! Please set at least one of:")
        print("   - GROQ_API_KEY")
        print("   - OPENAI_API_KEY") 
        print("   - GOOGLE_GEMINI_API_KEY")
        return
    
    print(f"📋 Available providers: {', '.join(available_providers)}")
    
    # Test each available provider
    results = {}
    all_comments = {}
    
    for provider in available_providers:
        success, comments = await test_provider_detailed(provider)
        results[provider] = success
        all_comments[provider] = comments
        
        # Add delay between providers
        if len(available_providers) > 1 and provider != available_providers[-1]:
            print(f"\n⏱️  Waiting 3 seconds before next provider...")
            await asyncio.sleep(3)
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL SUMMARY")
    print(f"{'='*60}")
    
    for provider, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        comment_count = len(all_comments.get(provider, [])) if all_comments.get(provider) else 0
        print(f"{provider.upper():10s}: {status} - {comment_count} comments generated")
    
    # Compare results if multiple providers worked
    working_providers = [p for p, success in results.items() if success and all_comments.get(p)]
    if len(working_providers) > 1:
        print(f"\n🔍 COMPARISON BETWEEN PROVIDERS:")
        for provider in working_providers:
            comments = all_comments[provider]
            print(f"\n{provider.upper()}:")
            for comment in comments[:3]:  # Show first 3 comments
                line_num = comment.get('line_number', 'N/A')
                comment_text = comment.get('comment', 'N/A')[:60] + "..."
                print(f"   Line {line_num}: {comment_text}")
    
    print(f"\n📁 Check 'llm_detailed_logs.log' for complete request/response details")
    print(f"🎉 Test completed!")

if __name__ == "__main__":
    asyncio.run(main())
