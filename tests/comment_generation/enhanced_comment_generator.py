import os
import j<PERSON>
from typing import List, Dict, Any, Optional
from app.llm.base import BaseDocGenerator
from app.utils.code_analyzer import CodeAnalyzer
from app.utils.language_detector import detect_language

class EnhancedCommentGenerator(BaseDocGenerator):
    """
    Enhanced comment generator that uses detailed code analysis to generate
    more contextual and informative comments.
    """
    
    def __init__(self, llm_generator: BaseDocGenerator):
        """
        Initialize with an LLM-based generator for the actual comment generation.
        
        Args:
            llm_generator: An instance of a BaseDocGenerator that uses an LLM
        """
        self.llm_generator = llm_generator
        self.code_analyzer = CodeAnalyzer()
        
    async def generate_file_documentation(self, file_content: str, file_path: str) -> Dict[str, str]:
        """
        Generate documentation for a single file with enhanced context.
        
        Args:
            file_content: The content of the file
            file_path: Path to the file
            
        Returns:
            Dictionary with 'inline_comments' and 'file_summary'
        """
        # First, analyze the code structure
        temp_file_path = self._save_temp_file(file_content)
        file_structure = self.code_analyzer.get_file_structure(temp_file_path)
        os.remove(temp_file_path)  # Clean up temp file
        
        # Generate comments with enhanced context
        enhanced_comments = await self._generate_enhanced_comments(file_content, file_path, file_structure)
        
        # Generate file summary using the LLM generator
        file_docs = await self.llm_generator.generate_file_documentation(file_content, file_path)
        file_summary = file_docs['file_summary']
        
        return {
            "inline_comments": enhanced_comments,
            "file_summary": file_summary
        }
    
    async def _generate_enhanced_comments(self, 
                                         file_content: str, 
                                         file_path: str, 
                                         file_structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate enhanced comments with detailed context from code analysis.
        
        Args:
            file_content: The content of the file
            file_path: Path to the file
            file_structure: Detailed structure information from code analysis
            
        Returns:
            List of comment dictionaries with line numbers and comment text
        """
        language = detect_language(file_path)
        
        # Prepare enhanced context for the LLM
        context = {
            "file_path": file_path,
            "language": language,
            "functions": file_structure.get('functions', []),
            "classes": file_structure.get('classes', []),
            "imports": file_structure.get('imports', []),
            "variables": file_structure.get('variables', []),
            "complexity_points": file_structure.get('complexity_points', [])
        }
        
        # Convert context to JSON string for the prompt
        context_json = json.dumps(context, indent=2)
        
        # Create an enhanced prompt with the context
        enhanced_prompt = f"""
        Analyze this {language} code and generate detailed inline comments.
        
        I'm providing additional context about the code structure:
        {context_json}
        
        For each comment, specify the line number where it should be inserted.
        Focus on:
        1. Function and class purposes
        2. Complex logic explanations
        3. Important variable initializations
        4. Key algorithm steps
        5. Potential edge cases or error handling
        
        Make comments concise but informative. Avoid stating the obvious.
        
        Code:
        {file_content}
        
        Format your response as a JSON array of objects with 'line_number' and 'comment' fields.
        Example:
        [
            {{"line_number": 10, "comment": "Initialize the database connection pool"}},
            {{"line_number": 15, "comment": "Execute the main query with retry logic"}}
        ]
        """
        
        # Use the LLM to generate comments with the enhanced context
        comments = await self.llm_generator.generate_inline_comments(enhanced_prompt, language)
        
        # If the LLM fails to generate proper comments, fall back to basic structure-based comments
        if not comments:
            comments = self._generate_fallback_comments(file_structure)
            
        return comments
    
    def _generate_fallback_comments(self, file_structure: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate basic comments based on code structure as a fallback.
        
        Args:
            file_structure: Detailed structure information from code analysis
            
        Returns:
            List of comment dictionaries with line numbers and comment text
        """
        comments = []
        
        # Add comments for functions
        for func in file_structure.get('functions', []):
            comments.append({
                "line_number": func['line'],
                "comment": f"Function: {func['name']} - {func.get('docstring', 'No description available')}"
            })
            
        # Add comments for classes
        for cls in file_structure.get('classes', []):
            comments.append({
                "line_number": cls['line'],
                "comment": f"Class: {cls['name']} - {cls.get('docstring', 'No description available')}"
            })
            
        # Add comments for complexity points
        for point in file_structure.get('complexity_points', []):
            comments.append({
                "line_number": point['line'],
                "comment": f"Complex logic: {point['content']}"
            })
            
        return comments
    
    def _save_temp_file(self, content: str) -> str:
        """
        Save content to a temporary file for analysis.
        
        Args:
            content: File content to save
            
        Returns:
            Path to the temporary file
        """
        import tempfile
        
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.py')
        temp_file.write(content.encode('utf-8'))
        temp_file.close()
        
        return temp_file.name
        
    async def generate_repo_summary(self, file_structure: List[Dict], key_files_content: Dict) -> str:
        """
        Generate high-level repository documentation.
        
        Args:
            file_structure: List of file information dictionaries
            key_files_content: Dictionary mapping file paths to their content
            
        Returns:
            Repository summary as a string
        """
        # Delegate to the LLM generator
        return await self.llm_generator.generate_repo_summary(file_structure, key_files_content)
        
    async def generate_inline_comments(self, code: str, language: str) -> List[Dict[str, Any]]:
        """
        Generate inline comments for code blocks.
        
        Args:
            code: The code content
            language: The programming language
            
        Returns:
            List of comment dictionaries with line numbers and comment text
        """
        # This is used directly by the LLM generator in _generate_enhanced_comments
        # For direct calls, delegate to the LLM generator
        return await self.llm_generator.generate_inline_comments(code, language)
