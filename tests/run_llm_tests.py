#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run all LLM-related tests.
"""

import os
import sys
import unittest
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_llm_tests():
    """Run all LLM-related tests."""
    logger.info("Running LLM tests...")
    
    # Discover and run all tests in the llm directory
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('llm', pattern='test_*.py')
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return the result
    return result

if __name__ == "__main__":
    # Run the LLM tests
    result = run_llm_tests()
    
    # Exit with appropriate code
    sys.exit(not result.wasSuccessful())
