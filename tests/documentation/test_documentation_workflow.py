#!/usr/bin/env python3
"""
Test script to verify the complete documentation generation workflow.
"""

import os
import sys
import tempfile
import shutil
import asyncio
import unittest
from unittest.mock import patch, MagicMock
import logging

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the necessary modules
from app.documentation.template_handler import TemplateDocGenerator
from app.documentation.enhanced_comment_generator import EnhancedCommentGenerator
from app.repo.repo_handler import RepoHandler
from app.llm import get_llm_generator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestDocumentationWorkflow(unittest.TestCase):
    """Test the complete documentation generation workflow."""

    def setUp(self):
        """Set up the test environment."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()
        
        # Create a test project structure
        self.project_dir = os.path.join(self.temp_dir, "test_project")
        os.makedirs(self.project_dir)
        
        # Create test files for different languages
        self.python_file = os.path.join(self.project_dir, "calculator.py")
        with open(self.python_file, 'w') as f:
            f.write("""
class Calculator:
    def add(self, a, b):
        return a + b

    def subtract(self, a, b):
        return a - b
""")
        
        self.cpp_file = os.path.join(self.project_dir, "calculator.cpp")
        with open(self.cpp_file, 'w') as f:
            f.write("""
class Calculator {
public:
    int add(int a, int b) {
        return a + b;
    }

    int subtract(int a, int b) {
        return a - b;
    }
};
""")
        
        # Initialize components
        self.repo_handler = RepoHandler()
        self.llm_generator = get_llm_generator()
        self.doc_generator = EnhancedCommentGenerator(self.llm_generator)

    def tearDown(self):
        """Clean up after the test."""
        shutil.rmtree(self.temp_dir)

    async def test_end_to_end_workflow(self):
        """Test the end-to-end documentation generation workflow."""
        # Step 1: Process the local directory
        logger.info("Step 1: Processing local directory")
        repo_path = self.repo_handler.process_local_directory(self.project_dir)
        self.assertTrue(os.path.exists(repo_path), f"Repository path {repo_path} does not exist")
        
        # Step 2: Get the file structure
        logger.info("Step 2: Getting file structure")
        file_structure = self.repo_handler.get_file_structure(repo_path)
        self.assertGreater(len(file_structure), 0, "File structure is empty")
        
        # Step 3: Generate documentation for each file
        logger.info("Step 3: Generating documentation for each file")
        processed_files = []
        
        for file_info in file_structure:
            # Skip non-code files
            if not self.repo_handler.is_code_file(file_info['path']):
                continue
                
            # Read file content
            with open(file_info['full_path'], 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Generate documentation
            file_docs = await self.doc_generator.generate_file_documentation(
                content,
                file_info['path']
            )
            
            # Verify documentation
            self.assertIsNotNone(file_docs['file_summary'], f"File summary is None for {file_info['path']}")
            self.assertGreater(len(file_docs['inline_comments']), 0, 
                              f"No inline comments generated for {file_info['path']}")
            
            # Insert comments
            updated_content = self.repo_handler.insert_comments(
                content,
                file_docs['inline_comments'],
                file_info['path'],
                comment_style='doxygen'
            )
            
            # Verify updated content
            self.assertNotEqual(content, updated_content, 
                               f"Content was not updated for {file_info['path']}")
            
            # Write updated content
            with open(file_info['full_path'], 'w', encoding='utf-8') as f:
                f.write(updated_content)
                
            processed_files.append(file_info['path'])
            
        # Step 4: Generate repository summary
        logger.info("Step 4: Generating repository summary")
        key_files_content = {}
        for file_path in processed_files:
            full_path = os.path.join(repo_path, file_path)
            with open(full_path, 'r', encoding='utf-8') as f:
                key_files_content[file_path] = f.read()
                
        repo_summary = await self.doc_generator.generate_repo_summary(
            file_structure,
            key_files_content
        )
        
        # Verify repository summary
        self.assertIsNotNone(repo_summary, "Repository summary is None")
        self.assertGreater(len(repo_summary), 0, "Repository summary is empty")
        
        logger.info("✅ End-to-end workflow test completed successfully")

def run_async_test(coro):
    """Run an async test."""
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(coro)

class AsyncTestCase(unittest.TestCase):
    """Base class for async tests."""
    def run_async(self, coro):
        """Run an async test."""
        return run_async_test(coro)

# Update the TestDocumentationWorkflow class to inherit from AsyncTestCase
TestDocumentationWorkflow.__bases__ = (AsyncTestCase,)

# Update the test methods to use run_async
_old_test_workflow = TestDocumentationWorkflow.test_end_to_end_workflow

def _new_test_workflow(self):
    return self.run_async(_old_test_workflow(self))

TestDocumentationWorkflow.test_end_to_end_workflow = _new_test_workflow

if __name__ == "__main__":
    unittest.main()
