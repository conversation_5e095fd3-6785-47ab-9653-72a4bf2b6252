#!/usr/bin/env python3
"""
Test script to demonstrate the LLM factory and document generation flow.
This script shows how to use different LLM providers and process a local directory.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Add the current directory to the path so we can import the app modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the necessary modules
from app.llm import get_llm_generator
from app.documentation.enhanced_comment_generator import EnhancedCommentGenerator
from app.repo import RepoHandler

def test_llm_factory():
    """Test the LLM factory by creating generators for different providers."""
    logger.info("Testing LLM factory...")
    
    # Try to create a generator for each provider
    providers = ['groq', 'gemini', 'template']
    
    for provider in providers:
        try:
            logger.info(f"Creating generator for provider: {provider}")
            generator = get_llm_generator(provider)
            logger.info(f"Successfully created generator for {provider}: {type(generator).__name__}")
        except Exception as e:
            logger.error(f"Failed to create generator for {provider}: {e}")
    
    # Try to create a generator with auto-detection
    try:
        logger.info("Creating generator with auto-detection...")
        generator = get_llm_generator()
        logger.info(f"Auto-detected provider: {type(generator).__name__}")
    except Exception as e:
        logger.error(f"Failed to create generator with auto-detection: {e}")

def process_test_directory(test_dir_path):
    """Process a test directory using the document generator."""
    logger.info(f"Processing test directory: {test_dir_path}")
    
    # Initialize components
    repo_handler = RepoHandler()
    
    try:
        # Get the LLM generator (auto-detect)
        llm_generator = get_llm_generator()
        logger.info(f"Using LLM generator: {type(llm_generator).__name__}")
        
        # Create the enhanced comment generator
        doc_generator = EnhancedCommentGenerator(llm_generator)
        
        # Process the local directory
        repo_path = repo_handler.process_local_directory(test_dir_path)
        logger.info(f"Test directory copied to: {repo_path}")
        
        # Get the file structure
        file_structure = repo_handler.get_file_structure(repo_path)
        logger.info(f"Found {len(file_structure)} files")
        
        # Process a single file as a test
        if file_structure:
            test_file = file_structure[0]
            logger.info(f"Processing test file: {test_file['path']}")
            
            # Read the file content
            with open(test_file['full_path'], 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Generate documentation
            file_docs = doc_generator.generate_file_documentation(content, test_file['path'])
            
            # Print the results
            logger.info("File documentation generated:")
            logger.info(f"File summary: {file_docs['file_summary']}")
            logger.info(f"Generated {len(file_docs['inline_comments'])} inline comments")
            
            # Update the file with comments
            updated_content = repo_handler.insert_comments(content, file_docs['inline_comments'], test_file['path'])
            
            # Write the updated content back to the file
            with open(test_file['full_path'], 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            logger.info(f"Updated file saved to: {test_file['full_path']}")
            
            # Generate repository summary
            repo_summary = doc_generator.generate_repo_summary(file_structure, {test_file['path']: file_docs['file_summary']})
            logger.info("Repository summary generated")
            
            # Write the summary to a README file
            readme_path = os.path.join(repo_path, "README.md")
            with open(readme_path, 'w', encoding='utf-8') as f:
                f.write(repo_summary)
            
            logger.info(f"README saved to: {readme_path}")
        
        return repo_path
    
    except Exception as e:
        logger.error(f"Error processing test directory: {e}")
        raise

if __name__ == "__main__":
    # Test the LLM factory
    test_llm_factory()
    
    # Create a test directory path
    test_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "test_project")
    
    # Check if the test directory exists
    if not os.path.exists(test_dir):
        logger.error(f"Test directory not found: {test_dir}")
        logger.info("Please create a test directory with some code files to process")
        sys.exit(1)
    
    # Process the test directory
    try:
        repo_path = process_test_directory(test_dir)
        logger.info(f"Test completed successfully. Results available at: {repo_path}")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        sys.exit(1)
