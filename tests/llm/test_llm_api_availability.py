#!/usr/bin/env python3
"""
Test script to verify that the LLM APIs are available and working correctly.
"""

import os
import sys
import unittest
import asyncio
import logging
from unittest.mock import patch

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the necessary modules
from app.llm import get_llm_generator
from app.llm.groq_handler import GroqDocGenerator
from app.llm.gemini_handler import GeminiDocGenerator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestLLMAPIAvailability(unittest.TestCase):
    """Test the availability of LLM APIs."""

    def test_api_keys_available(self):
        """Test that the API keys are available in the environment."""
        groq_api_key = os.getenv("GROQ_API_KEY")
        gemini_api_key = os.getenv("GOOGLE_GEMINI_API_KEY")
        
        # Check if at least one API key is available
        self.assertTrue(groq_api_key or gemini_api_key, 
                        "No LLM API keys found in environment variables")
        
        # Log which APIs are available
        if groq_api_key:
            logger.info("✅ Groq API key is available")
        else:
            logger.warning("⚠️ Groq API key is not available")
            
        if gemini_api_key:
            logger.info("✅ Gemini API key is available")
        else:
            logger.warning("⚠️ Gemini API key is not available")

    async def test_groq_api_connection(self):
        """Test that the Groq API connection works."""
        groq_api_key = os.getenv("GROQ_API_KEY")
        if not groq_api_key:
            self.skipTest("Groq API key not available")
            
        try:
            # Initialize the Groq generator
            groq_generator = GroqDocGenerator()
            
            # Test a simple API call
            test_code = "def hello_world():\n    print('Hello, World!')"
            comments = await groq_generator.generate_inline_comments(test_code, "Python")
            
            # Check that we got a valid response
            self.assertIsNotNone(comments)
            logger.info(f"✅ Groq API connection successful: {comments}")
            
        except Exception as e:
            self.fail(f"Groq API connection failed: {e}")

    async def test_gemini_api_connection(self):
        """Test that the Gemini API connection works."""
        gemini_api_key = os.getenv("GOOGLE_GEMINI_API_KEY")
        if not gemini_api_key:
            self.skipTest("Gemini API key not available")
            
        try:
            # Initialize the Gemini generator
            gemini_generator = GeminiDocGenerator()
            
            # Test a simple API call
            test_code = "def hello_world():\n    print('Hello, World!')"
            comments = await gemini_generator.generate_inline_comments(test_code, "Python")
            
            # Check that we got a valid response
            self.assertIsNotNone(comments)
            logger.info(f"✅ Gemini API connection successful: {comments}")
            
        except Exception as e:
            self.fail(f"Gemini API connection failed: {e}")

    def test_llm_factory_default_selection(self):
        """Test that the LLM factory selects the correct default generator."""
        try:
            # Get the default generator
            generator = get_llm_generator()
            
            # Check that we got a valid generator
            self.assertIsNotNone(generator)
            logger.info(f"✅ Default LLM generator: {type(generator).__name__}")
            
            # If both API keys are available, Groq should be preferred
            if os.getenv("GROQ_API_KEY") and os.getenv("GOOGLE_GEMINI_API_KEY"):
                self.assertIsInstance(generator, GroqDocGenerator, 
                                     "Groq should be the default when both APIs are available")
                
        except Exception as e:
            self.fail(f"LLM factory default selection failed: {e}")

def run_async_test(coro):
    """Run an async test."""
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(coro)

class AsyncTestCase(unittest.TestCase):
    """Base class for async tests."""
    def run_async(self, coro):
        """Run an async test."""
        return run_async_test(coro)

# Update the TestLLMAPIAvailability class to inherit from AsyncTestCase
TestLLMAPIAvailability.__bases__ = (AsyncTestCase,)

# Update the test methods to use run_async
_old_test_groq = TestLLMAPIAvailability.test_groq_api_connection
_old_test_gemini = TestLLMAPIAvailability.test_gemini_api_connection

def _new_test_groq(self):
    return self.run_async(_old_test_groq(self))

def _new_test_gemini(self):
    return self.run_async(_old_test_gemini(self))

TestLLMAPIAvailability.test_groq_api_connection = _new_test_groq
TestLLMAPIAvailability.test_gemini_api_connection = _new_test_gemini

if __name__ == "__main__":
    unittest.main()
