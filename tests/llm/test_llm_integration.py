#!/usr/bin/env python3
"""
Test script for LLM integration.
Tests the LLM factory and document generation with different providers.
"""

import os
import sys
import unittest
import logging
from unittest.mock import patch, MagicMock

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the necessary modules
from app.llm import get_llm_generator, LLMFactory
from app.documentation.enhanced_comment_generator import EnhancedCommentGenerator
from app.repo import RepoHandler

class TestLLMIntegration(unittest.TestCase):
    """Test the LLM integration and factory."""
    
    def setUp(self):
        """Set up the test environment."""
        # Create a test file content
        self.test_content = """
def factorial(n):
    if n < 0:
        raise ValueError("Factorial is not defined for negative numbers")
    if n == 0 or n == 1:
        return 1
    return n * factorial(n - 1)
"""
        self.test_file_path = "test_file.py"
    
    def test_llm_factory_registration(self):
        """Test that the LLM factory has registered providers."""
        providers = LLMFactory.list_available_providers()
        self.assertIn('groq', providers, "Groq provider should be registered")
        self.assertIn('gemini', providers, "Gemini provider should be registered")
        self.assertIn('template', providers, "Template provider should be registered")
    
    @patch('app.llm.LLMFactory._determine_provider_from_env')
    def test_auto_detection(self, mock_determine):
        """Test that the LLM factory auto-detects providers."""
        # Mock the auto-detection to return 'template'
        mock_determine.return_value = 'template'
        
        # Get a generator with auto-detection
        generator = get_llm_generator()
        
        # Check that the auto-detection was called
        mock_determine.assert_called_once()
        
        # Check that we got a template generator
        self.assertEqual(generator.__class__.__name__, 'TemplateDocGenerator')
    
    @patch('app.llm.base.BaseDocGenerator.generate_file_documentation')
    def test_enhanced_comment_generator(self, mock_generate):
        """Test that the enhanced comment generator works."""
        # Mock the generate_file_documentation method
        mock_generate.return_value = {
            'file_summary': 'Test summary',
            'inline_comments': [
                {'line_number': 2, 'comment': 'Test comment'}
            ]
        }
        
        # Create a generator
        llm_generator = get_llm_generator('template')
        doc_generator = EnhancedCommentGenerator(llm_generator)
        
        # Generate documentation
        result = doc_generator.generate_file_documentation(self.test_content, self.test_file_path)
        
        # Check the result
        self.assertEqual(result['file_summary'], 'Test summary')
        self.assertEqual(len(result['inline_comments']), 1)
        self.assertEqual(result['inline_comments'][0]['line_number'], 2)
        self.assertEqual(result['inline_comments'][0]['comment'], 'Test comment')
    
    def test_repo_handler_local_directory(self):
        """Test that the repo handler can process a local directory."""
        # Create a temporary directory
        import tempfile
        import shutil
        
        temp_dir = tempfile.mkdtemp()
        try:
            # Create a test file in the temporary directory
            test_file_path = os.path.join(temp_dir, "test_file.py")
            with open(test_file_path, 'w') as f:
                f.write(self.test_content)
            
            # Initialize the repo handler
            repo_handler = RepoHandler()
            
            # Process the local directory
            repo_path = repo_handler.process_local_directory(temp_dir)
            
            # Check that the directory was processed
            self.assertTrue(os.path.exists(repo_path))
            
            # Check that the file structure can be retrieved
            file_structure = repo_handler.get_file_structure(repo_path)
            self.assertEqual(len(file_structure), 1)
            self.assertEqual(file_structure[0]['path'], 'test_file.py')
            
            # Clean up
            repo_handler.cleanup(repo_path, require_confirmation=False)
        finally:
            # Clean up the temporary directory
            shutil.rmtree(temp_dir)

if __name__ == '__main__':
    unittest.main()
