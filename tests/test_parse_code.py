# New file for testing code parsing
import pytest
from app.documentation.template_handler import TemplateDocGenerator
from app.llm.base import CodeParser  # Fix missing import for CodeParser

def test_python_function_parsing():
    parser = CodeParser()
    code = """
def hello_world():
    \"\"\"This is a test function.\"\"\"
    print("Hello, World!")
    """
    result = parser._parse_python(code)
    assert len(result['entities']) == 1
    assert result['entities'][0]['name'] == 'hello_world'