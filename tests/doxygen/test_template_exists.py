#!/usr/bin/env python3
"""
Simple test to verify that the prompt.j2 template exists and the old templates have been removed.
"""

import os
import unittest

class TestTemplateFiles(unittest.TestCase):
    """Test the template files."""

    def test_prompt_template_exists(self):
        """Test that the prompt.j2 template exists."""
        # Get the absolute path to the project root
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
        template_path = os.path.join(project_root, "docs", "templates", "prompt.j2")
        self.assertTrue(os.path.exists(template_path), f"Template file {template_path} does not exist")

    def test_old_templates_removed(self):
        """Test that the old templates have been removed."""
        # Get the absolute path to the project root
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
        file_template_path = os.path.join(project_root, "docs", "templates", "file.md.j2")
        repo_template_path = os.path.join(project_root, "docs", "templates", "repository.md.j2")

        self.assertFalse(os.path.exists(file_template_path), f"Old template file {file_template_path} still exists")
        self.assertFalse(os.path.exists(repo_template_path), f"Old template file {repo_template_path} still exists")

if __name__ == "__main__":
    unittest.main()
