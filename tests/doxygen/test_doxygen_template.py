#!/usr/bin/env python3
"""
Test script to verify that the Doxygen comment generation works correctly with the new template.
"""

import os
import sys
import tempfile
import shutil
import asyncio
import unittest
from unittest.mock import patch, MagicMock

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# Import the necessary modules
from app.documentation.template_handler import TemplateDocGenerator
from app.repo.repo_handler import RepoHandler

class TestDoxygenTemplate(unittest.TestCase):
    """Test the Doxygen template and comment generation."""

    def setUp(self):
        """Set up the test environment."""
        # Create a temporary directory for testing
        self.temp_dir = tempfile.mkdtemp()

        # Create test files for different languages
        self.python_content = """
class Calculator:
    def add(self, a, b):
        return a + b

    def subtract(self, a, b):
        return a - b
"""
        self.cpp_content = """
class Calculator {
public:
    int add(int a, int b) {
        return a + b;
    }

    int subtract(int a, int b) {
        return a - b;
    }
};
"""
        self.js_content = """
class Calculator {
    add(a, b) {
        return a + b;
    }

    subtract(a, b) {
        return a - b;
    }
}
"""

        # Save the test files
        self.python_file = os.path.join(self.temp_dir, "calculator.py")
        with open(self.python_file, 'w') as f:
            f.write(self.python_content)

        self.cpp_file = os.path.join(self.temp_dir, "calculator.cpp")
        with open(self.cpp_file, 'w') as f:
            f.write(self.cpp_content)

        self.js_file = os.path.join(self.temp_dir, "calculator.js")
        with open(self.js_file, 'w') as f:
            f.write(self.js_content)

        # Initialize the template handler
        self.template_handler = TemplateDocGenerator(
            templates_dir="docs/templates",
            output_dir="docs/generated"
        )

        # Initialize the repo handler
        self.repo_handler = RepoHandler()

    def tearDown(self):
        """Clean up after the test."""
        shutil.rmtree(self.temp_dir)

    async def test_python_doxygen_comments(self):
        """Test Doxygen comment generation for Python files."""
        # Generate documentation
        file_docs = await self.template_handler.generate_file_documentation(
            self.python_content,
            "calculator.py"
        )

        # Verify that the file summary is generated
        self.assertIsNotNone(file_docs['file_summary'])
        self.assertIn("Doxygen", file_docs['file_summary'])

        # Verify that inline comments are generated
        self.assertGreater(len(file_docs['inline_comments']), 0)

        # Insert the comments into the file
        updated_content = self.repo_handler.insert_comments(
            self.python_content,
            file_docs['inline_comments'],
            "calculator.py",
            comment_style='doxygen'
        )

        # Verify that the comments are inserted correctly
        self.assertIn('"""!', updated_content)
        self.assertIn('@brief', updated_content)

    async def test_cpp_doxygen_comments(self):
        """Test Doxygen comment generation for C++ files."""
        # Generate documentation
        file_docs = await self.template_handler.generate_file_documentation(
            self.cpp_content,
            "calculator.cpp"
        )

        # Verify that the file summary is generated
        self.assertIsNotNone(file_docs['file_summary'])
        self.assertIn("Doxygen", file_docs['file_summary'])

        # Verify that inline comments are generated
        self.assertGreater(len(file_docs['inline_comments']), 0)

        # Insert the comments into the file
        updated_content = self.repo_handler.insert_comments(
            self.cpp_content,
            file_docs['inline_comments'],
            "calculator.cpp",
            comment_style='doxygen'
        )

        # Verify that the comments are inserted correctly
        self.assertIn('/**', updated_content)
        self.assertIn(' * @brief', updated_content)

    async def test_js_doxygen_comments(self):
        """Test Doxygen comment generation for JavaScript files."""
        # Generate documentation
        file_docs = await self.template_handler.generate_file_documentation(
            self.js_content,
            "calculator.js"
        )

        # Verify that the file summary is generated
        self.assertIsNotNone(file_docs['file_summary'])
        self.assertIn("Doxygen", file_docs['file_summary'])

        # Verify that inline comments are generated
        self.assertGreater(len(file_docs['inline_comments']), 0)

        # Insert the comments into the file
        updated_content = self.repo_handler.insert_comments(
            self.js_content,
            file_docs['inline_comments'],
            "calculator.js",
            comment_style='doxygen'
        )

        # Verify that the comments are inserted correctly
        self.assertIn('/**', updated_content)
        self.assertIn(' * @brief', updated_content)

    def test_prompt_template_exists(self):
        """Test that the prompt.j2 template exists."""
        template_path = os.path.join("docs", "templates", "prompt.j2")
        self.assertTrue(os.path.exists(template_path), f"Template file {template_path} does not exist")

    def test_old_templates_removed(self):
        """Test that the old templates have been removed."""
        file_template_path = os.path.join("docs", "templates", "file.md.j2")
        repo_template_path = os.path.join("docs", "templates", "repository.md.j2")

        self.assertFalse(os.path.exists(file_template_path), f"Old template file {file_template_path} still exists")
        self.assertFalse(os.path.exists(repo_template_path), f"Old template file {repo_template_path} still exists")

def run_async_test(coro):
    """Run an async test."""
    loop = asyncio.get_event_loop()
    return loop.run_until_complete(coro)

class AsyncTestCase(unittest.TestCase):
    """Base class for async tests."""
    def run_async(self, coro):
        """Run an async test."""
        return run_async_test(coro)

# Update the TestDoxygenTemplate class to inherit from AsyncTestCase
TestDoxygenTemplate.__bases__ = (AsyncTestCase,)

# Update the test methods to use run_async
_old_test_python = TestDoxygenTemplate.test_python_doxygen_comments
_old_test_cpp = TestDoxygenTemplate.test_cpp_doxygen_comments
_old_test_js = TestDoxygenTemplate.test_js_doxygen_comments

def _new_test_python(self):
    return self.run_async(_old_test_python(self))

def _new_test_cpp(self):
    return self.run_async(_old_test_cpp(self))

def _new_test_js(self):
    return self.run_async(_old_test_js(self))

TestDoxygenTemplate.test_python_doxygen_comments = _new_test_python
TestDoxygenTemplate.test_cpp_doxygen_comments = _new_test_cpp
TestDoxygenTemplate.test_js_doxygen_comments = _new_test_js

if __name__ == "__main__":
    unittest.main()
