#!/usr/bin/env python3
"""
Simple test script for Doxygen comment generation using the template-based generator.
"""

import os
import asyncio
import logging
import json
from app.documentation.template_handler import TemplateDocGenerator
from app.repo import <PERSON>oHandler
from app.utils.code_analyzer import CodeAnalyzer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Sample Python code for testing
SAMPLE_CODE = '''
import os
import sys
from typing import List, Dict, Optional

class DataProcessor:
    """A class for processing data files."""

    def __init__(self, input_dir: str, output_dir: str):
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.processed_files = 0

    def process_file(self, filename: str) -> bool:
        """Process a single file and return success status."""
        try:
            input_path = os.path.join(self.input_dir, filename)
            output_path = os.path.join(self.output_dir, f"processed_{filename}")

            # Read the input file
            with open(input_path, 'r') as f:
                content = f.read()

            # Apply some transformations
            processed_content = content.upper()

            # Write to output file
            with open(output_path, 'w') as f:
                f.write(processed_content)

            self.processed_files += 1
            return True
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            return False

    def process_all_files(self) -> Dict[str, int]:
        """Process all files in the input directory."""
        results = {"success": 0, "failed": 0}

        for filename in os.listdir(self.input_dir):
            if os.path.isfile(os.path.join(self.input_dir, filename)):
                success = self.process_file(filename)
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1

        return results

def main():
    # Parse command line arguments
    if len(sys.argv) != 3:
        print("Usage: python script.py input_dir output_dir")
        return

    input_dir = sys.argv[1]
    output_dir = sys.argv[2]

    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Initialize and run the processor
    processor = DataProcessor(input_dir, output_dir)
    results = processor.process_all_files()

    # Print results
    print(f"Processing complete. Success: {results['success']}, Failed: {results['failed']}")

if __name__ == "__main__":
    main()
'''

def generate_doxygen_comments(file_structure):
    """Generate Doxygen-style comments based on code structure."""
    comments = []

    # Add file header comment
    comments.append({
        "line_number": 1,
        "comment": f"/**\n * @file {os.path.basename(file_structure.get('path', 'unknown'))}\n * @brief Auto-generated file documentation\n */"
    })

    # Add comments for functions
    for func in file_structure.get('functions', []):
        comment = f"/**\n * @brief {func.get('name', 'Unknown function')}\n"
        # Add parameters if available
        # Add return if available
        comment += " */"
        comments.append({
            "line_number": func['line'],
            "comment": comment
        })

    # Add comments for classes
    for cls in file_structure.get('classes', []):
        comments.append({
            "line_number": cls['line'],
            "comment": f"/**\n * @brief {cls.get('name', 'Unknown class')}\n * @details Class implementation\n */"
        })

    return comments

async def test_doxygen_simple():
    """Test simple Doxygen comment generation."""
    logger.info("Testing Simple Doxygen Comment Generation")

    # Initialize the code analyzer
    code_analyzer = CodeAnalyzer()

    # Save sample code to a temporary file
    temp_file_path = "temp_sample.py"
    with open(temp_file_path, "w") as f:
        f.write(SAMPLE_CODE)

    try:
        # Analyze the code structure
        logger.info("Analyzing code structure...")
        file_structure = code_analyzer.get_file_structure(temp_file_path)

        # Generate Doxygen comments
        logger.info("Generating Doxygen comments...")
        doxygen_comments = generate_doxygen_comments(file_structure)

        # Display the generated comments
        logger.info("\nGenerated Doxygen Comments:")
        for comment in doxygen_comments:
            logger.info(f"Line {comment['line_number']}: {comment['comment']}")

        # Insert comments into the code
        repo_handler = RepoHandler("./data/repo")
        updated_content = repo_handler.insert_comments(
            SAMPLE_CODE,
            doxygen_comments,
            temp_file_path,
            comment_style='doxygen'  # Use Doxygen comment style
        )

        # Save the updated code
        output_file_path = "temp_sample_with_doxygen_comments.py"
        with open(output_file_path, "w") as f:
            f.write(updated_content)

        logger.info(f"\nCode with Doxygen comments saved to: {output_file_path}")

    finally:
        # Clean up temporary files
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

if __name__ == "__main__":
    asyncio.run(test_doxygen_simple())
