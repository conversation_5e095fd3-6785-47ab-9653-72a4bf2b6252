#!/usr/bin/env python3
"""
Test script for generating proper Python-style Doxygen comments.
This script demonstrates the correct format for Doxygen-compatible comments in Python.
"""

import os
import asyncio
import logging
import json
from app.repo import <PERSON>oHand<PERSON>
from app.utils.code_analyzer import <PERSON>Analy<PERSON>

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Sample Python code for testing
SAMPLE_CODE = '''
import os
import sys
from typing import List, Dict, Optional

class DataProcessor:
    """A class for processing data files."""

    def __init__(self, input_dir: str, output_dir: str):
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.processed_files = 0

    def process_file(self, filename: str) -> bool:
        """Process a single file and return success status."""
        try:
            input_path = os.path.join(self.input_dir, filename)
            output_path = os.path.join(self.output_dir, f"processed_{filename}")

            # Read the input file
            with open(input_path, 'r') as f:
                content = f.read()

            # Apply some transformations
            processed_content = content.upper()

            # Write to output file
            with open(output_path, 'w') as f:
                f.write(processed_content)

            self.processed_files += 1
            return True
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            return False

    def process_all_files(self) -> Dict[str, int]:
        """Process all files in the input directory."""
        results = {"success": 0, "failed": 0}

        for filename in os.listdir(self.input_dir):
            if os.path.isfile(os.path.join(self.input_dir, filename)):
                success = self.process_file(filename)
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1

        return results

def main():
    # Parse command line arguments
    if len(sys.argv) != 3:
        print("Usage: python script.py input_dir output_dir")
        return

    input_dir = sys.argv[1]
    output_dir = sys.argv[2]

    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Initialize and run the processor
    processor = DataProcessor(input_dir, output_dir)
    results = processor.process_all_files()

    # Print results
    print(f"Processing complete. Success: {results['success']}, Failed: {results['failed']}")

if __name__ == "__main__":
    main()
'''

def generate_python_doxygen_comments(file_structure):
    """
    Generate Python-style Doxygen comments based on code structure.
    Uses the ## style comments which are recommended for Python.
    """
    comments = []

    # Add file header comment
    comments.append({
        "line_number": 1,
        "comment": "##\n# @file temp_sample.py\n# @brief Script to process text files by converting contents to uppercase.\n##"
    })

    # Add comments for classes
    for cls in file_structure.get('classes', []):
        class_name = cls.get('name', 'Unknown')
        comments.append({
            "line_number": cls['line'],
            "comment": f"##\n# @class {class_name}\n# @brief Handles processing of files from an input directory and saves them to an output directory.\n##"
        })

    # Add comments for functions
    for func in file_structure.get('functions', []):
        func_name = func.get('name', 'Unknown')

        # Special handling for different function types
        if func_name == "__init__":
            comment = "##\n# @brief Constructor.\n"
            comment += "# @param input_dir Path to the input directory containing text files.\n"
            comment += "# @param output_dir Path to the output directory for processed files.\n##"
        elif func_name == "process_file":
            comment = "##\n# @brief Processes a single file.\n"
            comment += "# @param filename Name of the file to process.\n"
            comment += "# @return True if processing was successful, False otherwise.\n##"
        elif func_name == "process_all_files":
            comment = "##\n# @brief Processes all files in the input directory.\n"
            comment += "# @return A dictionary containing counts of successful and failed file processes.\n##"
        elif func_name == "main":
            comment = "##\n# @brief Main entry point. Parses arguments and runs the processing logic.\n##"
        else:
            # Generic function comment
            comment = f"##\n# @brief {func_name} function.\n##"

        comments.append({
            "line_number": func['line'],
            "comment": comment
        })

    return comments

def insert_python_doxygen_comments(content, comments):
    """
    Insert Python-style Doxygen comments into the code.
    This function handles the insertion directly rather than using RepoHandler
    to ensure proper formatting for Python-style Doxygen comments.
    """
    lines = content.split('\n')

    # Sort comments by line number in reverse order
    # (to avoid line number shifts when inserting)
    sorted_comments = sorted(
        comments,
        key=lambda x: x['line_number'],
        reverse=True
    )

    # Insert comments
    for comment in sorted_comments:
        line_num = comment['line_number'] - 1  # Convert to 0-based index
        if 0 <= line_num < len(lines):
            # Insert the comment as-is (it's already in the correct format)
            lines.insert(line_num, comment['comment'])

    return '\n'.join(lines)

async def test_python_doxygen():
    """Test Python-style Doxygen comment generation."""
    logger.info("Testing Python-style Doxygen Comment Generation")

    # Initialize the code analyzer
    code_analyzer = CodeAnalyzer()

    # Save sample code to a temporary file
    temp_file_path = "temp_sample.py"
    with open(temp_file_path, "w") as f:
        f.write(SAMPLE_CODE)

    try:
        # Analyze the code structure
        logger.info("Analyzing code structure...")
        file_structure = code_analyzer.get_file_structure(temp_file_path)

        # Generate Python-style Doxygen comments
        logger.info("Generating Python-style Doxygen comments...")
        doxygen_comments = generate_python_doxygen_comments(file_structure)

        # Display the generated comments
        logger.info("\nGenerated Python-style Doxygen Comments:")
        for comment in doxygen_comments:
            logger.info(f"Line {comment['line_number']}: {comment['comment']}")

        # Insert comments into the code
        updated_content = insert_python_doxygen_comments(SAMPLE_CODE, doxygen_comments)

        # Save the updated code
        output_file_path = "temp_sample_with_python_doxygen.py"
        with open(output_file_path, "w") as f:
            f.write(updated_content)

        logger.info(f"\nCode with Python-style Doxygen comments saved to: {output_file_path}")

    finally:
        # Clean up temporary files
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

if __name__ == "__main__":
    asyncio.run(test_python_doxygen())
