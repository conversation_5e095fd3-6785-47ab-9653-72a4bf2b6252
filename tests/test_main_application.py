import asyncio
from app.repo import <PERSON><PERSON><PERSON><PERSON><PERSON>, RepositoryDownloadError

async def main():
    repo_url = "https://github.com/octocat/Hello-World"  # Replace with a valid repository URL
    repo_handler = RepoHandler()

    try:
        # Test downloading the repository
        print(f"Downloading repository from {repo_url}...")
        repo_path = await repo_handler.download_repository(repo_url)
        print(f"Repository downloaded to: {repo_path}")

        # Test retrieving the file structure
        print("Retrieving file structure...")
        file_structure = repo_handler.get_file_structure(repo_path)
        for file_info in file_structure:
            print(file_info)

        # Clean up the downloaded repository
        print("Cleaning up...")
        repo_handler.cleanup(repo_path)
        print("Cleanup complete.")

    except RepositoryDownloadError as e:
        print(f"Error: {e}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
