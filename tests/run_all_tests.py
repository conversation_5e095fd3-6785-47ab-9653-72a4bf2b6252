#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run all tests for the application.
"""

import os
import sys
import unittest
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_all_tests():
    """Run all tests for the application."""
    logger.info("Running all tests...")
    
    # Discover and run all tests
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('.', pattern='test_*.py')
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return the result
    return result

def run_specific_tests(test_modules):
    """Run specific test modules."""
    logger.info(f"Running specific tests: {test_modules}")
    
    # Create a test suite
    test_suite = unittest.TestSuite()
    
    # Add the specified test modules
    for module_name in test_modules:
        try:
            # Import the module
            module = __import__(module_name)
            
            # Add all tests from the module
            test_suite.addTest(unittest.defaultTestLoader.loadTestsFromModule(module))
            
        except ImportError:
            logger.error(f"Could not import test module: {module_name}")
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return the result
    return result

if __name__ == "__main__":
    # Check if specific test modules are specified
    if len(sys.argv) > 1:
        # Run specific test modules
        test_modules = sys.argv[1:]
        result = run_specific_tests(test_modules)
    else:
        # Run all tests
        result = run_all_tests()
    
    # Exit with appropriate code
    sys.exit(not result.wasSuccessful())
