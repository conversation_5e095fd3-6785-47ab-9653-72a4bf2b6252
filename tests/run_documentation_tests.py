#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to run all documentation-related tests.
"""

import os
import sys
import unittest
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_documentation_tests():
    """Run all documentation-related tests."""
    logger.info("Running documentation tests...")
    
    # Discover and run all tests in the documentation and doxygen directories
    test_loader = unittest.TestLoader()
    
    # Create a test suite
    test_suite = unittest.TestSuite()
    
    # Add tests from the documentation directory
    doc_tests = test_loader.discover('documentation', pattern='test_*.py')
    test_suite.addTest(doc_tests)
    
    # Add tests from the doxygen directory
    doxygen_tests = test_loader.discover('doxygen', pattern='test_*.py')
    test_suite.addTest(doxygen_tests)
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)
    
    # Return the result
    return result

if __name__ == "__main__":
    # Run the documentation tests
    result = run_documentation_tests()
    
    # Exit with appropriate code
    sys.exit(not result.wasSuccessful())
