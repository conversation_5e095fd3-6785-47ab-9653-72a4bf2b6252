import requests
import json
import sys
import time
import os
import webbrowser
from dotenv import load_dotenv
from app.utils.language_detector import detect_language

# Load environment variables
load_dotenv()

def get_repository_contents(owner, repo, path="", headers=None):
    """Recursively get repository contents"""
    contents_url = f"https://api.github.com/repos/{owner}/{repo}/contents/{path}"
    response = requests.get(contents_url, headers=headers)

    if response.status_code != 200:
        print(f"❌ Error accessing path {path}: {response.text}")
        return []

    contents = response.json()
    if not isinstance(contents, list):
        contents = [contents]

    result = []
    for item in contents:
        if item['type'] == 'dir':
            # Recursively get contents of directories
            sub_contents = get_repository_contents(owner, repo, item['path'], headers)
            result.extend(sub_contents)
        else:
            result.append(item)
    return result

def test_documentation_generator():
    url = "http://localhost:8000/docs/generate"

    print("\n=== Starting OAuth Authentication ===")
    print("🔑 Starting GitHub authorization...")

    # Get repository URL from user
    default_repo = "https://github.com/jainsahabtusharjain/Dashboard"
    print(f"\nCurrent repository URL: {default_repo}")
    print("Would you like to use a different repository? (y/n)")
    if input().lower() == 'y':
        repo_url = input("Enter the GitHub repository URL: ").strip()
        if not repo_url:
            print("❌ Error: Repository URL cannot be empty")
            return
        if not repo_url.startswith("https://github.com/"):
            print("❌ Error: Please enter a valid GitHub repository URL")
            return
    else:
        repo_url = default_repo

    # Use the provided access token directly
    access_token = "****************************************"
    print("\n✅ Using provided access token")

    # Now proceed with documentation generation
    print("\n=== Starting Documentation Generation ===")

    # First verify repository access
    print("🔍 Verifying repository access...")
    try:
        # Extract owner and repo from URL
        repo_parts = repo_url.replace("https://github.com/", "").split("/")
        if len(repo_parts) != 2:
            print("❌ Error: Invalid repository URL format")
            return
        owner, repo = repo_parts

        # Try to access the repository first
        repo_check_url = f"https://api.github.com/repos/{owner}/{repo}"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/vnd.github.v3+json"
        }
        repo_response = requests.get(repo_check_url, headers=headers)

        if repo_response.status_code != 200:
            print("❌ Error: Cannot access repository")
            print("Please make sure:")
            print("1. The repository exists")
            print("2. You have access to the repository")
            print("3. The access token has the 'repo' scope")
            print(f"GitHub API Response: {repo_response.text}")
            return

        print("✅ Repository access verified")

        # Recursively check repository contents
        print("\n📁 Checking repository contents recursively...")
        all_contents = get_repository_contents(owner, repo, headers=headers)

        if not all_contents:
            print("⚠️ Warning: No files found in repository")
        else:
            print(f"\nFound {len(all_contents)} files in repository:")

            # Group files by type
            file_types = {}
            for item in all_contents:
                file_type = item['name'].split('.')[-1] if '.' in item['name'] else 'no_extension'
                if file_type not in file_types:
                    file_types[file_type] = []
                file_types[file_type].append(item['path'])

            # Print files grouped by type
            for file_type, files in file_types.items():
                print(f"\n{file_type.upper()} files ({len(files)}):")
                for file_path in files:
                    print(f"- {file_path}")

            # Check for specific directories
            src_files = [f for f in all_contents if f['path'].startswith('src/')]
            if src_files:
                print(f"\nFound {len(src_files)} files in src directory:")
                for file in src_files:
                    print(f"- {file['path']}")

            # Check for configuration files
            config_files = [f for f in all_contents if any(f['path'].endswith(ext) for ext in ['.json', '.js', '.ts', '.config.js'])]
            if config_files:
                print(f"\nFound {len(config_files)} configuration files:")
                for file in config_files:
                    print(f"- {file['path']}")

    except Exception as e:
        print(f"❌ Error checking repository access: {str(e)}")
        return

    payload = {
        "repo_url": repo_url,
        "access_token": access_token
    }

    try:
        print("\n⏳ Sending request to documentation generator...")
        print(f"Repository URL: {payload['repo_url']}")

        # Increased timeout to 5 minutes and enable streaming
        response = requests.post(
            url,
            json=payload,
            timeout=300,  # 5 minutes timeout
            stream=True
        )

        if response.status_code != 200:
            print(f"\n❌ Error: Server returned status code {response.status_code}")
            error_detail = ""
            try:
                error_data = response.json()
                error_detail = error_data.get("detail", response.text)
            except:
                error_detail = response.text
            print(f"Error details: {error_detail}")
            print("\nPlease check:")
            print("1. The repository URL is correct")
            print("2. The repository contains valid source code files")
            print("3. You have the necessary permissions")
            print("\nDebug information:")
            print(f"Repository URL: {payload['repo_url']}")
            print(f"Access token: {access_token[:10]}...")  # Only show first 10 chars for security
            return

        # Process streaming response
        start_time = time.time()
        last_update = start_time
        spinner = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        i = 0

        print("\n📥 Downloading repository...")
        for line in response.iter_lines():
            if line:
                try:
                    # Try to parse as JSON progress update
                    update = json.loads(line)
                    if 'progress' in update:
                        print(f"\r{update['progress']}")
                    continue
                except json.JSONDecodeError:
                    pass

            # Show spinner for activity
            if time.time() - last_update >= 0.1:
                elapsed = int(time.time() - start_time)
                sys.stdout.write(f'\r{spinner[i]} Processing... ({elapsed}s elapsed)')
                sys.stdout.flush()
                i = (i + 1) % len(spinner)
                last_update = time.time()

        print("\n\n📋 Final Result:")
        try:
            result = response.json()
        except json.JSONDecodeError:
            print("❌ Error: Could not parse server response")
            print("Raw response:", response.text)
            return

        if result.get("status") == "success":
            print("✅ Documentation generated successfully!")
            print(f"📁 Documentation saved to: {result.get('documentation_path', 'docs/generated/')}")

            try:
                doc_path = result.get('documentation_path', 'docs/generated/generated_docs.md')
                with open(doc_path, 'r') as f:
                    print("\n📄 Documentation Preview:")
                    print("=" * 50)
                    print(f.read()[:500] + "...")
                    print("=" * 50)
            except Exception as e:
                print(f"\n⚠️ Couldn't read documentation file: {e}")
        else:
            print("\n❌ Error generating documentation:")
            print(result.get("error", "Unknown error"))

    except requests.exceptions.Timeout:
        print("\n⚠️ Request timed out after 5 minutes.")
        print("💡 The repository might be too large or the server is busy.")
        print("   Try again or process a smaller repository first.")

    except requests.exceptions.RequestException as e:
        print(f"\n❌ Error making request: {e}")
        print("💡 Make sure the FastAPI server is running on http://localhost:8000")

    except KeyboardInterrupt:
        print("\n\n🛑 Operation cancelled by user")
        print("💡 The server might still be processing in the background")

    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")

def test_clone_repository():
    from app.repo import RepoHandler
    repo_handler = RepoHandler()
    repo_url = "https://github.com/jainsahabtusharjain/Dashboard"
    repo_path = repo_handler.download_repository(repo_url)
    assert os.path.exists(repo_path)

def test_generate_inline_documentation():
    from app.documentation.template_handler import TemplateDocGenerator
    doc_generator = TemplateDocGenerator(templates_dir="docs/templates", output_dir="docs/generated")
    file_content = "def hello_world():\n    print('Hello, World!')"
    file_docs = doc_generator.generate_file_documentation(file_content, "hello.py")
    assert "inline_comments" in file_docs
    assert "file_summary" in file_docs

if __name__ == "__main__":
    try:
        test_documentation_generator()
    except KeyboardInterrupt:
        print("\n\n👋 Exiting gracefully...")
        sys.exit(0)