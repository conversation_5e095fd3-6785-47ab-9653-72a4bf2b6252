#!/usr/bin/env python3
"""
<PERSON>ript to run all Doxygen-related tests.
"""

import os
import sys
import unittest
import asyncio

# Add the project root to the path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def run_tests():
    """Run all Doxygen-related tests."""
    # Discover and run all tests in the doxygen directory
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('doxygen', pattern='test_*.py')
    
    # Run the tests
    test_runner = unittest.TextTestRunner(verbosity=2)
    test_runner.run(test_suite)

if __name__ == "__main__":
    run_tests()
