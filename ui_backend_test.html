<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Integration Test - Doxygen Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 900px;
            width: 95%;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .status-bar {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }

        .status-bar h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .status-bar p {
            color: #666;
            font-size: 14px;
            margin: 2px 0;
        }

        .input-section {
            margin-bottom: 30px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        input[type="text"], input[type="url"], select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="url"]:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            width: 100%;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .progress-section {
            display: none;
            margin-top: 30px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e1e5e9;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
        }

        .progress-text {
            text-align: center;
            color: #666;
            font-size: 14px;
        }

        .results-section {
            display: none;
            margin-top: 30px;
        }

        .result-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .result-item h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .result-item p {
            color: #666;
            font-size: 14px;
        }

        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #e1e5e9;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #c3e6cb;
        }

        .code-preview {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }

        .diff-view {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .diff-original, .diff-commented {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .diff-original h5, .diff-commented h5 {
            margin-bottom: 10px;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Backend Integration Test</h1>
            <p>Test your Doxygen Generator backend functionality</p>
        </div>

        <!-- Backend Status -->
        <div class="status-bar" id="status-bar">
            <h4>🔌 Backend Status</h4>
            <p id="backend-status">Checking connection...</p>
            <p id="api-keys-status">Checking API keys...</p>
        </div>

        <div class="tabs">
            <div class="tab active" onclick="switchTab('single')">Single File Test</div>
            <div class="tab" onclick="switchTab('test')">Backend Tests</div>
        </div>

        <!-- Single File Tab -->
        <div id="single-tab" class="tab-content active">
            <div class="input-section">
                <div class="input-group">
                    <label for="single-file">Upload Code File:</label>
                    <input type="file" id="single-file" accept=".py,.cpp,.c,.h,.hpp,.js,.java,.cs,.php,.rb,.go,.rs,.kt,.swift">
                </div>
                
                <div class="input-group">
                    <label for="single-provider">LLM Provider:</label>
                    <select id="single-provider">
                        <option value="groq">Groq (deepseek-r1-distill-llama-70b)</option>
                        <option value="gemini">Google Gemini 2.0 Flash</option>
                        <option value="openai">OpenAI GPT</option>
                    </select>
                </div>
                
                <div class="input-group">
                    <label>
                        <input type="checkbox" id="use-doxygen" checked> Use Doxygen Generator (Recommended)
                    </label>
                </div>
                
                <button class="btn" onclick="processSingleFile()">Generate Comments</button>
            </div>
        </div>

        <!-- Test Backend Tab -->
        <div id="test-tab" class="tab-content">
            <div class="input-section">
                <h3>🧪 Test Backend Functionality</h3>
                <p>Use these tests to verify your backend is working correctly:</p>
                
                <button class="btn" onclick="testBackendConnection()">Test Backend Connection</button>
                <button class="btn btn-secondary" onclick="testSampleCode()">Test with Sample Python Code</button>
                <button class="btn btn-secondary" onclick="testSampleCppCode()">Test with Sample C++ Code</button>
                
                <div class="input-group" style="margin-top: 20px;">
                    <label for="test-code">Or test with custom code:</label>
                    <textarea id="test-code" rows="10" placeholder="def hello_world():
    print('Hello, World!')

class Calculator:
    def add(self, a, b):
        return a + b"></textarea>
                    <button class="btn btn-secondary" onclick="testCustomCode()">Test Custom Code</button>
                </div>
            </div>
        </div>

        <!-- Progress Section -->
        <div class="progress-section" id="progress-section">
            <h3>Processing...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="progress-text" id="progress-text">Initializing...</div>
        </div>

        <!-- Results Section -->
        <div class="results-section" id="results-section">
            <h3>Results</h3>
            <div id="results-container"></div>
        </div>
    </div>

    <script>
        // API Base URL
        const API_BASE = 'http://localhost:8000';

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🧪 Backend Integration Test UI loaded');
            checkBackendStatus();
        });

        // Backend status check
        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE}/`);
                const data = await response.json();
                
                document.getElementById('backend-status').innerHTML = '✅ Backend connected successfully';
                document.getElementById('backend-status').style.color = '#28a745';
                document.getElementById('api-keys-status').innerHTML = '🔑 Check your CLI output for API key status';
                
            } catch (error) {
                document.getElementById('backend-status').innerHTML = '❌ Backend connection failed - Make sure your FastAPI server is running on port 8000';
                document.getElementById('backend-status').style.color = '#dc3545';
                document.getElementById('api-keys-status').innerHTML = '⚠️ Cannot check API keys - Backend offline';
            }
        }

        // Tab switching
        function switchTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(tabName + '-tab').classList.add('active');
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
