App Description

### AI-Powered Doxygen Documentation Generator Magic App

This Magic App enables developers to automatically generate Doxygen-compatible comments and full Doxygen documentation (HTML and Markdown) for source code across multiple languages within a repository or local directory.

## Core Features

1. Input Methods
   - Link to repository (GitHub URL)
   - Local directory upload (zip/tar)

2. Language Support
   - Mixed-language projects (C, C++, Java, Python, etc.)
   - Detect file type and apply appropriate comment style

3. Output Formats
   - HTML (standard Doxygen output)
   - Markdown

4. Customization
   - No theme customization required; uses standard Doxygen styling

## UI Overview

1. **Home Page**
   - Fields:
     - Repository URL input
     - File upload control for local directory archive
   - Buttons:
     - "Analyze & Generate Comments"
     - "Generate Documentation"

2. **Progress & Status**
   - Real-time progress bar or status updates for scanning, comment generation, and docs generation

3. **Results View**
   - Preview pane for generated Doxygen comments inserted into source files
   - Links to download:
     - Comment-enhanced source code archive
     - Doxygen HTML documentation bundle
     - Markdown documentation bundle

## Loops (API Functions)

1. `analyze_project`
   - Input: repository URL or file archive
   - Action: clone or unpack, scan files, detect languages
   - Output: file list and language mapping

2. `generate_comments`
   - Input: scanned file list
   - Action: call AI model to insert Doxygen-style comments into each file
   - Output: archive of commented source files

3. `generate_documentation`
   - Input: commented source archive
   - Action: run Doxygen to produce HTML, convert docs to Markdown
   - Output: HTML bundle, Markdown files

4. `download_output`
   - Input: selection of output type
   - Action: return downloadable URL for selected file bundle
   - Output: file URL

## Technical Notes

- Stateless functions using client-side storage for temporary state
- No backend database required
- Uses standard Doxygen toolchain inside serverless function environment

## User Flow

1. User inputs a GitHub URL or uploads a zip of their code.
2. App scans and detects languages.
3. User triggers comment generation.
4. AI loop inserts Doxygen comments.
5. User generates documentation in HTML or Markdown.
6. User downloads the outputs.

This outline covers all your requirements for input methods, multi-language support, output formats, and simplicity without theme customization. Let’s build it!


You can use markdown formatting and edit JSON examples directly



Name
Doxygen DocGen AI


Interface
A clean developer UI that lets users point to a codebase by entering a repository URL or uploading a zip/tar archive. The home view shows two input controls side by side: a text field for the GitHub (or other Git) URL with a ‘Scan Project’ button, and a file upload control for a .zip or .tar archive with an ‘Upload & Scan’ button. After scanning begins, display a progress bar and a live status log area indicating steps: cloning/unpacking, language detection, file indexing. Once scanning completes, reveal two action buttons: ‘Generate Doxygen Comments’ and ‘Generate Documentation’. “Generate Doxygen Comments” triggers AI insertion of Doxygen-style comments into each source file; show a progress bar and then display a preview pane with a side-by-side diff view of original vs commented code for a selected file. Provide a “Download Commented Source” button. “Generate Documentation” triggers the doc build pipeline and shows progress for HTML generation and Markdown conversion. After it finishes, show download links for the HTML bundle and the Markdown bundle. Always include a reset or start-over button in the header.



