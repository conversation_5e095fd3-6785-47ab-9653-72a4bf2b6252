import os
import shutil
import logging
import tempfile
from typing import List, Optional, Dict
from . import git_operations
from app.utils.language_detector import detect_language

class LocalDirectoryError(Exception):
    """Raised when there's an issue with a local directory"""
    pass

class RepoHandler:
    def __init__(self, temp_dir: Optional[str] = None):
        self.temp_dir = temp_dir or os.getenv('REPO_TEMP_DIR', '/data/repo')
        os.makedirs(self.temp_dir, exist_ok=True)
        logger = logging.getLogger(__name__)
        logger.info(f"Repository storage initialized at: {self.temp_dir}")

    def get_file_structure(self, repo_path: str) -> List[Dict]:
        """Get the structure of files in the repository, filtering by supported extensions."""
        file_structure = []
        for root, _, files in os.walk(repo_path):
            for file in files:
                full_path = os.path.join(root, file)
                rel_path = os.path.relpath(full_path, repo_path)

                # Filter files based on their extensions
                if detect_language(file) != 'Unknown':
                    file_structure.append({
                        'path': rel_path,
                        'full_path': full_path
                    })
        return file_structure

    def cleanup(self, repo_path: str, require_confirmation: bool = False):
        """
        Clean up downloaded repository with detailed logging and optional confirmation.

        Args:
            repo_path: Path to repository directory
            require_confirmation: If True, will log warning but not delete unless confirmed
        """
        logger = logging.getLogger(__name__)
        logger.info(f"Starting cleanup for repository: {repo_path}")

        if not os.path.exists(repo_path):
            logger.warning(f"Repository path does not exist: {repo_path}")
            return

        if require_confirmation:
            logger.warning(f"⚠️ Confirmation required before deleting: {repo_path}")
            logger.warning(f"Directory contains:")
            for root, dirs, files in os.walk(repo_path):
                for name in files:
                    logger.warning(f"• {os.path.join(root, name)}")
            return

        # Detailed logging of files being removed
        total_files = 0
        total_size = 0
        for root, dirs, files in os.walk(repo_path):
            for name in files:
                file_path = os.path.join(root, name)
                file_size = os.path.getsize(file_path)
                logger.debug(f"Removing file: {file_path} ({file_size} bytes)")
                total_files += 1
                total_size += file_size

        logger.info(f"Removing {total_files} files (total {total_size} bytes) from {repo_path}")
        shutil.rmtree(repo_path, ignore_errors=True)
        logger.info(f"Cleanup completed for repository: {repo_path}")

    def insert_comments(self, content: str, comments: List[Dict[str, any]], file_path: str, comment_style: str = 'doxygen') -> str:
        """
        Insert comments into the source code at specified line numbers.

        Args:
            content: Original file content
            comments: List of {'line_number': int, 'comment': str}
            file_path: Path to the file (used to determine comment syntax)
            comment_style: Style of comments to insert ('standard' or 'doxygen')

        Returns:
            Updated file content with inserted comments
        """
        from app.utils.language_detector import detect_language

        # Get the language from the file path
        language = detect_language(file_path)

        # Determine comment syntax based on file extension
        ext = file_path.split('.')[-1].lower()
        comment_syntax = {
            'py': '#',
            'js': '//',
            'ts': '//',
            'java': '//',
            'cpp': '//',
            'c': '//',
            'cs': '//',
            'go': '//',
            'rb': '#',
            'php': '//'
        }.get(ext, '#')

        # Split content into lines
        lines = content.split('\n')

        # Sort comments by line number in reverse order
        # (to avoid line number shifts when inserting)
        sorted_comments = sorted(
            comments,
            key=lambda x: x['line_number'],
            reverse=True
        )

        # Insert comments
        for comment in sorted_comments:
            line_num = comment['line_number'] - 1  # Convert to 0-based index
            if 0 <= line_num < len(lines):
                comment_text = comment['comment']

                if comment_style == 'doxygen':
                    # For Doxygen comments, ensure they have the proper format based on language
                    if language == 'Python':
                        # Python Doxygen comments
                        if not (comment_text.startswith('"""!') or comment_text.startswith('##!')):
                            # Format as Python Doxygen docstring
                            comment_text = f'"""!\n{comment_text}\n"""'
                    elif language in ['C++', 'Java', 'JavaScript', 'C', 'C#']:
                        # C-style Doxygen comments
                        if not (comment_text.startswith('/**') or comment_text.startswith('///')):
                            # Format as C-style Doxygen comment
                            comment_text = '/**\n * ' + comment_text.replace("\n", "\n * ") + '\n */'
                    else:
                        # For other languages, use standard comment style if not already formatted
                        if not (comment_text.startswith('/**') or comment_text.startswith('///') or
                                comment_text.startswith('"""!') or comment_text.startswith('##!')):
                            comment_text = f"{comment_syntax} {comment_text}"
                else:
                    # Standard comment style
                    comment_text = f"{comment_syntax} {comment_text}"

                lines.insert(line_num, comment_text)

        return '\n'.join(lines)

    def process_local_directory(self, local_path: str) -> str:
        """
        Process a local directory instead of downloading a repository.
        Creates a copy of the directory in the temp directory to avoid modifying the original.

        Args:
            local_path: Path to the local directory

        Returns:
            Path to the copied directory in the temp directory

        Raises:
            LocalDirectoryError: If the directory doesn't exist or can't be copied
        """
        logger = logging.getLogger(__name__)

        # Validate the local path
        if not os.path.exists(local_path):
            raise LocalDirectoryError(f"Local directory does not exist: {local_path}")

        if not os.path.isdir(local_path):
            raise LocalDirectoryError(f"Path is not a directory: {local_path}")

        # Create a unique directory name based on the original path
        dir_name = os.path.basename(os.path.normpath(local_path))
        temp_dir_path = os.path.join(self.temp_dir, f"{dir_name}_{tempfile.mktemp(prefix='', dir='').split('/')[-1]}")

        logger.info(f"Copying local directory: {local_path} to {temp_dir_path}")

        try:
            # Copy the directory to the temp directory
            shutil.copytree(local_path, temp_dir_path)
            return temp_dir_path
        except Exception as e:
            raise LocalDirectoryError(f"Failed to copy local directory: {e}")

    async def download_repository(self, repo_url: str, branch: Optional[str] = None, tag: Optional[str] = None) -> str:
        """
        Process a repository URL or local directory path.
        If the URL is a local path, process it as a local directory.
        Otherwise, download the repository from GitHub or GitLab.

        Args:
            repo_url: Git repository URL or local directory path
            branch: Specific branch to download (defaults to default branch)
            tag: Specific tag to download (takes precedence over branch)

        Returns:
            Path to the cloned repository or copied directory

        Raises:
            LocalDirectoryError: If there's an issue with a local directory
            git_operations.GitOperationError: If there's an issue with Git operations
        """
        # Check if the input is a local directory path
        if os.path.exists(repo_url) and os.path.isdir(repo_url):
            return self.process_local_directory(repo_url)

        # Otherwise, treat it as a Git repository URL
        repo_dir = tempfile.mkdtemp(dir=self.temp_dir)

        try:
            # Use the git_operations module to download the repository
            return await git_operations.download_repository(repo_url, repo_dir, branch, tag)
        except Exception as e:
            shutil.rmtree(repo_dir, ignore_errors=True)
            raise git_operations.GitOperationError(f"Failed to download repository: {e}")

    def download_repository(self, repo_url: str) -> str:
        """
        Synchronous version of download_repository for backward compatibility.

        Args:
            repo_url: Git repository URL or local directory path

        Returns:
            Path to the cloned repository or copied directory
        """
        # Check if the input is a local directory path
        if os.path.exists(repo_url) and os.path.isdir(repo_url):
            return self.process_local_directory(repo_url)

        # Otherwise, treat it as a Git repository URL
        repo_url = repo_url.rstrip('/').replace('.git', '')
        repo_name = repo_url.split('/')[-1]
        repo_path = os.path.join(self.temp_dir, repo_name)

        if os.path.exists(repo_path):
            shutil.rmtree(repo_path)

        try:
            return git_operations.clone_repository(repo_url, repo_path)
        except Exception as e:
            raise git_operations.GitOperationError(f"Failed to clone repository: {e}")
