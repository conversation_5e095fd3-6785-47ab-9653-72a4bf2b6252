import git
"""
Git operations module for handling repository cloning, committing, and pushing.
"""
import os
import logging
import requests
import aiohttp
from typing import Optional, Dict
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class GitOperationError(Exception):
    """Raised when a Git operation fails"""
    pass

class UnsupportedRepositoryHost(Exception):
    """Raised when repository host is not supported"""
    pass

async def clone_repository(repo_url: str, target_dir: str) -> str:
    """
    Clone a Git repository to the target directory.
    
    Args:
        repo_url: Repository URL
        target_dir: Target directory to clone into
        
    Returns:
        Path to the cloned repository
        
    Raises:
        GitOperationError: If cloning fails
    """
    try:
        import git
        git.Repo.clone_from(repo_url, target_dir)
        return target_dir
    except Exception as e:
        raise GitOperationError(f"Failed to clone repository: {e}")

async def download_github_repo(owner: str, repo: str, target_dir: str, ref: Optional[str] = None) -> str:
    """
    Download repository from GitHub with optional branch/tag reference.
    
    Args:
        owner: Repository owner
        repo: Repository name
        target_dir: Target directory to download into
        ref: Branch or tag reference (optional)
        
    Returns:
        Path to the downloaded repository
    """
    headers = {}
    if os.getenv('GITHUB_TOKEN'):
        headers['Authorization'] = f"token {os.getenv('GITHUB_TOKEN')}"

    # If no ref specified, get the default branch
    if not ref:
        api_url = f"https://api.github.com/repos/{owner}/{repo}"
        async with aiohttp.ClientSession() as session:
            async with session.get(api_url, headers=headers) as response:
                if response.status == 404:
                    raise GitOperationError(f"Repository {owner}/{repo} not found")
                response.raise_for_status()
                repo_info = await response.json()
                ref = repo_info.get('default_branch', 'main')

    # Get the contents with the specified ref
    contents_url = f"https://api.github.com/repos/{owner}/{repo}/contents?ref={ref}"
    await download_github_contents(contents_url, target_dir)
    return target_dir

async def download_github_contents(api_url: str, target_dir: str):
    """
    Recursively download GitHub repository contents.
    
    Args:
        api_url: GitHub API URL for the contents
        target_dir: Target directory to download into
    """
    headers = {}
    if os.getenv('GITHUB_TOKEN'):
        headers['Authorization'] = f"token {os.getenv('GITHUB_TOKEN')}"

    async with aiohttp.ClientSession() as session:
        async with session.get(api_url, headers=headers) as response:
            if response.status == 404:
                logger.warning(f"Warning: Path not found: {api_url}")
                return
            response.raise_for_status()
            contents = await response.json()

            for item in contents:
                item_path = os.path.join(target_dir, item['path'])

                if item['type'] == 'dir':
                    os.makedirs(item_path, exist_ok=True)
                    # Get the tree URL for the directory
                    tree_url = item['url'].replace('/contents', '/git/trees') + '?recursive=1'
                    await download_github_contents(tree_url, item_path)
                else:
                    try:
                        file_response = requests.get(item['download_url'], headers=headers)
                        file_response.raise_for_status()

                        os.makedirs(os.path.dirname(item_path), exist_ok=True)
                        with open(item_path, 'wb') as f:
                            f.write(file_response.content)
                    except Exception as e:
                        logger.warning(f"Warning: Failed to download {item['path']}: {str(e)}")
                        continue

async def download_gitlab_repo(owner: str, repo: str, target_dir: str, ref: Optional[str] = None) -> str:
    """
    Download repository from GitLab with optional branch/tag reference.
    
    Args:
        owner: Repository owner
        repo: Repository name
        target_dir: Target directory to download into
        ref: Branch or tag reference (optional)
        
    Returns:
        Path to the downloaded repository
    """
    # GitLab requires the repository ID or path with namespace
    repo_path = f"{owner}/{repo}"
    encoded_path = requests.utils.quote(repo_path, safe='')

    # If no ref specified, get the default branch
    if not ref:
        api_url = f"https://gitlab.com/api/v4/projects/{encoded_path}"
        response = requests.get(api_url, headers={'PRIVATE-TOKEN': os.getenv('GITLAB_TOKEN', '')})
        response.raise_for_status()
        repo_info = response.json()
        ref = repo_info.get('default_branch', 'main')

    api_url = f"https://gitlab.com/api/v4/projects/{encoded_path}/repository/tree?ref={ref}"
    await download_gitlab_contents(owner, repo, encoded_path, target_dir, ref)
    return target_dir

async def download_gitlab_contents(owner: str, repo: str, encoded_path: str, target_dir: str, path: str = ""):
    """
    Recursively download GitLab repository contents.
    
    Args:
        owner: Repository owner
        repo: Repository name
        encoded_path: URL-encoded repository path
        target_dir: Target directory to download into
        path: Path within the repository (optional)
    """
    headers = {}
    if os.getenv('GITLAB_TOKEN'):
        headers['PRIVATE-TOKEN'] = os.getenv('GITLAB_TOKEN')

    # Get the tree structure
    api_url = f"https://gitlab.com/api/v4/projects/{encoded_path}/repository/tree"
    params = {'path': path} if path else {}
    response = requests.get(api_url, headers=headers, params=params)
    response.raise_for_status()

    contents = response.json()
    for item in contents:
        item_path = os.path.join(target_dir, item['path'])

        if item['type'] == 'tree':
            os.makedirs(item_path, exist_ok=True)
            await download_gitlab_contents(owner, repo, encoded_path, target_dir, item['path'])
        else:
            # Download file content
            file_url = f"https://gitlab.com/api/v4/projects/{encoded_path}/repository/files/{requests.utils.quote(item['path'], safe='')}/raw"
            file_response = requests.get(file_url, headers=headers)
            file_response.raise_for_status()

            os.makedirs(os.path.dirname(item_path), exist_ok=True)
            with open(item_path, 'wb') as f:
                f.write(file_response.content)

def commit_and_push_changes(repo_path: str, branch_name: str = "documentation-update", commit_message: str = "Add documentation") -> bool:
    """
    Commit and push changes to a Git repository.
    
    Args:
        repo_path: Path to the repository
        branch_name: Name of the branch to create
        commit_message: Commit message
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Create a new branch
        result = os.system(f"cd {repo_path} && git checkout -b {branch_name}")
        if result != 0:
            logger.error(f"Failed to create branch: {branch_name}")
            return False
            
        # Add all changes
        result = os.system(f"cd {repo_path} && git add .")
        if result != 0:
            logger.error("Failed to add changes")
            return False
            
        # Commit changes
        result = os.system(f"cd {repo_path} && git commit -m '{commit_message}'")
        if result != 0:
            logger.error("Failed to commit changes")
            return False
            
        # Push changes
        result = os.system(f"cd {repo_path} && git push origin {branch_name}")
        if result != 0:
            logger.error(f"Failed to push changes to branch: {branch_name}")
            return False
            
        return True
    except Exception as e:
        logger.error(f"Error during Git operations: {e}")
        return False

async def download_repository(repo_url: str, target_dir: str, branch: Optional[str] = None, tag: Optional[str] = None) -> str:
    """
    Download repository contents from GitHub or GitLab with optional branch/tag.
    Returns the path to the downloaded repository.

    Args:
        repo_url: Repository URL
        target_dir: Target directory to download into
        branch: Specific branch to download (defaults to default branch)
        tag: Specific tag to download (takes precedence over branch)

    Raises:
        GitOperationError: If download fails with specific error details
        UnsupportedRepositoryHost: If host is not GitHub/GitLab
        ValueError: For invalid URLs or parameters
    """
    if tag and branch:
        raise ValueError("Cannot specify both branch and tag - tag takes precedence")

    try:
        # Clean up the URL by removing .git extension and any trailing slashes
        repo_url = repo_url.rstrip('/').replace('.git', '')

        parsed_url = urlparse(repo_url)
        path_parts = parsed_url.path.strip('/').split('/')

        if len(path_parts) < 2:
            raise ValueError("Repository URL must include owner and repo name")

        owner, repo = path_parts[:2]

        # Determine if it's GitHub or GitLab
        if 'github.com' in parsed_url.netloc:
            ref = tag or branch
            return await download_github_repo(owner, repo, target_dir, ref)
        elif 'gitlab.com' in parsed_url.netloc:
            ref = tag or branch
            return await download_gitlab_repo(owner, repo, target_dir, ref)
        else:
            raise UnsupportedRepositoryHost(
                f"Host '{parsed_url.netloc}' not supported. Only GitHub and GitLab are supported."
            )

    except requests.HTTPError as e:
        status_code = e.response.status_code if hasattr(e, 'response') else None
        raise GitOperationError(
            f"HTTP Error {status_code} downloading repository: {str(e)}"
        )
    except Exception as e:
        raise GitOperationError(
            f"Failed to download repository: {str(e)}\n"
            f"URL: {repo_url}\n"
            f"Branch: {branch or 'default'}\n"
            f"Tag: {tag or 'none'}"
        )
