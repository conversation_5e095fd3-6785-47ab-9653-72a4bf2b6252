from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import RedirectResponse, JSONResponse
import httpx
import os
from dotenv import load_dotenv
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

load_dotenv()

# Create router without prefix - we'll mount it at the root level
router = APIRouter()

# OAuth configuration
GITHUB_CLIENT_ID = os.getenv("GITHUB_CLIENT_ID")
GITHUB_CLIENT_SECRET = os.getenv("GITHUB_CLIENT_SECRET")
GITLAB_CLIENT_ID = os.getenv("GITLAB_CLIENT_ID")
GITLAB_CLIENT_SECRET = os.getenv("GITLAB_CLIENT_SECRET")
BASE_URL = "http://localhost:8000"

@router.get("/auth/login/github")
async def github_login():
    """Initiate GitHub OAuth flow"""
    logger.info("Starting GitHub OAuth login flow")
    
    if not GITHUB_CLIENT_ID:
        logger.error("GitHub Client ID not configured")
        raise HTTPException(status_code=500, detail="GitHub Client ID not configured")
    
    # GitHub OAuth endpoint with the correct callback URL
    callback_url = f"{BASE_URL}/auth/github/callback"
    github_auth_url = (
        f"https://github.com/login/oauth/authorize"
        f"?client_id={GITHUB_CLIENT_ID}"
        f"&redirect_uri={callback_url}"
        f"&scope=repo"
    )
    
    logger.info(f"Redirecting to GitHub with callback URL: {callback_url}")
    return RedirectResponse(url=github_auth_url)

@router.get("/login/gitlab")
async def login_gitlab():
    """Redirect to GitLab OAuth login"""
    if not GITLAB_CLIENT_ID:
        raise HTTPException(status_code=500, detail="GitLab OAuth not configured")
    
    return RedirectResponse(
        f"https://gitlab.com/oauth/authorize"
        f"?client_id={GITLAB_CLIENT_ID}"
        f"&redirect_uri={BASE_URL}/callback/gitlab"
        "&response_type=code"
        "&scope=read_repository"
    )

@router.get("/auth/github/callback")
async def github_callback(request: Request):
    """Handle GitHub OAuth callback."""
    try:
        # Extract the authorization code from the query parameters
        code = request.query_params.get("code")
        if not code:
            return {"error": "Authorization code not found in the callback response"}

        # Exchange the authorization code for an access token
        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                "https://github.com/login/oauth/access_token",
                headers={"Accept": "application/json"},
                data={
                    "client_id": GITHUB_CLIENT_ID,
                    "client_secret": GITHUB_CLIENT_SECRET,
                    "code": code,
                    "redirect_uri": f"{BASE_URL}/auth/github/callback"
                }
            )
            token_response.raise_for_status()
            token_data = token_response.json()

        # Check if the access token is present in the response
        access_token = token_data.get("access_token")
        if not access_token:
            return {"error": "Failed to retrieve access token from GitHub"}

        # Save the token to a file for later use
        with open(".auth_token", "w") as token_file:
            token_file.write(access_token)

        logger.info("Successfully obtained and saved access token.")
        return {"message": "Successfully obtained access token", "access_token": access_token}
    except httpx.HTTPError as e:
        logger.error(f"HTTP error during token exchange: {e}")
        return {"error": "Failed to exchange authorization code for access token"}
    except Exception as e:
        logger.error(f"Error handling GitHub callback: {e}")
        return {"error": "Failed to handle callback"}

@router.get("/callback/gitlab")
async def callback_gitlab(code: str, request: Request):
    """Handle GitLab OAuth callback"""
    if not GITLAB_CLIENT_ID or not GITLAB_CLIENT_SECRET:
        raise HTTPException(status_code=500, detail="GitLab OAuth not configured")
    
    try:
        async with httpx.AsyncClient() as client:
            # Exchange code for access token
            response = await client.post(
                "https://gitlab.com/oauth/token",
                data={
                    "client_id": GITLAB_CLIENT_ID,
                    "client_secret": GITLAB_CLIENT_SECRET,
                    "code": code,
                    "redirect_uri": f"{BASE_URL}/callback/gitlab",
                    "grant_type": "authorization_code"
                }
            )
            response.raise_for_status()
            token_data = response.json()
            
            # Get user information
            user_response = await client.get(
                "https://gitlab.com/api/v4/user",
                headers={"Authorization": f"Bearer {token_data['access_token']}"}
            )
            user_response.raise_for_status()
            user_data = user_response.json()
            
            # Store the token and user data (implement your storage method)
            # For now, we'll just return it
            return {
                "access_token": token_data["access_token"],
                "token_type": token_data["token_type"],
                "user": user_data
            }
            
    except httpx.HTTPError as e:
        raise HTTPException(status_code=400, detail=f"OAuth error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")