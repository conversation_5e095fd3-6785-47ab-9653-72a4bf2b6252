from typing import Dict

def detect_language(file_path: str) -> str:
    """
    Detect programming language from file extension.
    
    Args:
        file_path (str): Path to the file
        
    Returns:
        str: Detected programming language name
    """
    ext = file_path.split('.')[-1].lower()
    language_map: Dict[str, str] = {
        'py': 'Python',
        'js': 'JavaScript',
        'ts': 'TypeScript',
        'java': 'Java',
        'cpp': 'C++',
        'c': 'C',
        'cs': 'C#',
        'go': 'Go',
        'rb': 'Ruby',
        'php': 'PHP',
        'html': 'HTML',
        'css': 'CSS',
        'json': 'JSON',
        'yaml': 'YAML',
        'yml': 'YAML',
        'md': 'Markdown',
        'rst': 'reStructuredText',
        'sh': 'Shell',
        'bash': 'Shell',
        'sql': 'SQL',
        'xml': 'XML'
    }
    return language_map.get(ext, 'Unknown') 