import os
import re
import collections
from typing import Dict, List, Set, Optional, Any, DefaultDict
from app.utils.language_detector import detect_language

# --- Configuration ---

# File extensions to analyze for code/comment lines
# Format: { 'extension': { 'line_comment': '#', 'block_comment_start': None, 'block_comment_end': None } }
SUPPORTED_EXTENSIONS = {
    '.py': {'line_comment': '#', 'block_comment_start': '"""', 'block_comment_end': '"""'},
    '.js': {'line_comment': '//', 'block_comment_start': '/*', 'block_comment_end': '*/'},
    '.java': {'line_comment': '//', 'block_comment_start': '/*', 'block_comment_end': '*/'},
    '.c': {'line_comment': '//', 'block_comment_start': '/*', 'block_comment_end': '*/'},
    '.cpp': {'line_comment': '//', 'block_comment_start': '/*', 'block_comment_end': '*/'},
    '.h': {'line_comment': '//', 'block_comment_start': '/*', 'block_comment_end': '*/'},
    '.html': {'line_comment': None, 'block_comment_start': '<!--', 'block_comment_end': '-->'},
    '.css': {'line_comment': None, 'block_comment_start': '/*', 'block_comment_end': '*/'},
    # Add more language configurations here
}

# Directories to ignore during analysis
IGNORE_DIRS = {'.git', '__pycache__', 'node_modules', 'venv', '.venv', 'build', 'dist', 'target'}

# Files to ignore
IGNORE_FILES = {'.DS_Store'}

class CodeAnalyzer:
    """
    Analyzes code files to extract structure and metrics for documentation generation.
    """
    
    def __init__(self, ignore_dirs: Optional[Set[str]] = None, ignore_files: Optional[Set[str]] = None):
        """
        Initialize the code analyzer with optional custom ignore settings.
        
        Args:
            ignore_dirs: Set of directory names to ignore
            ignore_files: Set of file names to ignore
        """
        self.ignore_dirs = ignore_dirs if ignore_dirs is not None else IGNORE_DIRS
        self.ignore_files = ignore_files if ignore_files is not None else IGNORE_FILES
    
    def analyze_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Analyzes a single file for line counts and basic structure.

        Args:
            file_path: The path to the file

        Returns:
            Dictionary containing analysis results for the file, or None if unsupported/error
        """
        _, extension = os.path.splitext(file_path)
        extension = extension.lower()

        if extension not in SUPPORTED_EXTENSIONS:
            return None  # Not a supported code file

        lang_config = SUPPORTED_EXTENSIONS[extension]
        line_comment_start = lang_config.get('line_comment')
        block_comment_start = lang_config.get('block_comment_start')
        block_comment_end = lang_config.get('block_comment_end')

        results = {
            'total_lines': 0,
            'blank_lines': 0,
            'comment_lines': 0,
            'code_lines': 0,
            'functions': [],
            'classes': [],
            'imports': [],
            'variables': [],
            'line_mapping': [],  # Will store line type for each line (code, comment, blank)
            'complexity_points': []  # Will store lines with potentially complex logic
        }

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                in_block_comment = False
                lines = f.readlines()
                
                # First pass: count lines and identify basic structures
                for i, line in enumerate(lines):
                    line_num = i + 1  # 1-based line numbering
                    results['total_lines'] += 1
                    stripped_line = line.strip()
                    line_info = {'line_num': line_num, 'type': 'code', 'content': line.rstrip()}

                    # --- Line Counting Logic ---
                    if not stripped_line:
                        results['blank_lines'] += 1
                        line_info['type'] = 'blank'
                        results['line_mapping'].append(line_info)
                        continue

                    is_comment_line = False

                    # Check block comments
                    if block_comment_start and block_comment_end:
                        if in_block_comment:
                            results['comment_lines'] += 1
                            is_comment_line = True
                            line_info['type'] = 'comment'
                            # Check if the block comment ends on this line
                            if block_comment_end in stripped_line:
                                in_block_comment = False
                        elif stripped_line.startswith(block_comment_start):
                            results['comment_lines'] += 1
                            is_comment_line = True
                            line_info['type'] = 'comment'
                            # Check if it's a single-line block comment
                            if not (block_comment_end in stripped_line):
                                in_block_comment = True

                    # Check line comments
                    if not is_comment_line and line_comment_start and stripped_line.startswith(line_comment_start):
                        results['comment_lines'] += 1
                        is_comment_line = True
                        line_info['type'] = 'comment'

                    # --- Structure Identification ---
                    if not is_comment_line:
                        # Python-specific analysis
                        if extension == '.py':
                            # Find function definitions
                            func_match = re.match(r'^\s*def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(', stripped_line)
                            if func_match:
                                func_name = func_match.group(1)
                                results['functions'].append({
                                    'name': func_name,
                                    'line': line_num,
                                    'docstring': self._extract_python_docstring(lines, i)
                                })
                                line_info['structure'] = 'function'
                                line_info['name'] = func_name

                            # Find class definitions
                            class_match = re.match(r'^\s*class\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*[:\(]', stripped_line)
                            if class_match:
                                class_name = class_match.group(1)
                                results['classes'].append({
                                    'name': class_name,
                                    'line': line_num,
                                    'docstring': self._extract_python_docstring(lines, i)
                                })
                                line_info['structure'] = 'class'
                                line_info['name'] = class_name
                                
                            # Find import statements
                            import_match = re.match(r'^\s*(import|from)\s+([a-zA-Z0-9_.]+)', stripped_line)
                            if import_match:
                                import_type = import_match.group(1)
                                import_module = import_match.group(2)
                                results['imports'].append({
                                    'type': import_type,
                                    'module': import_module,
                                    'line': line_num
                                })
                                line_info['structure'] = 'import'
                                
                            # Find variable assignments
                            var_match = re.match(r'^\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(.+)$', stripped_line)
                            if var_match and not stripped_line.startswith('def ') and not stripped_line.startswith('class '):
                                var_name = var_match.group(1)
                                var_value = var_match.group(2).strip()
                                results['variables'].append({
                                    'name': var_name,
                                    'value': var_value,
                                    'line': line_num
                                })
                                line_info['structure'] = 'variable'
                                line_info['name'] = var_name
                                
                        # JavaScript/TypeScript analysis
                        elif extension in ['.js', '.ts']:
                            # Find function definitions (including arrow functions)
                            func_match = re.match(r'^\s*(function\s+([a-zA-Z_][a-zA-Z0-9_]*)|const\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=\s*(\([^)]*\)|[a-zA-Z_][a-zA-Z0-9_]*)\s*=>)', stripped_line)
                            if func_match:
                                func_name = func_match.group(2) if func_match.group(2) else func_match.group(3)
                                results['functions'].append({
                                    'name': func_name,
                                    'line': line_num,
                                    'type': 'arrow' if '=>' in stripped_line else 'function'
                                })
                                line_info['structure'] = 'function'
                                line_info['name'] = func_name
                                
                            # Find class definitions
                            class_match = re.match(r'^\s*class\s+([a-zA-Z_][a-zA-Z0-9_]*)', stripped_line)
                            if class_match:
                                class_name = class_match.group(1)
                                results['classes'].append({
                                    'name': class_name,
                                    'line': line_num
                                })
                                line_info['structure'] = 'class'
                                line_info['name'] = class_name
                        
                        # Identify potentially complex logic across languages
                        if any(keyword in stripped_line for keyword in ['if ', 'for ', 'while ', 'switch', 'try', 'catch']):
                            results['complexity_points'].append({
                                'line': line_num,
                                'content': stripped_line
                            })
                            line_info['complexity'] = True
                    
                    results['line_mapping'].append(line_info)

                # Calculate code lines
                results['code_lines'] = results['total_lines'] - results['blank_lines'] - results['comment_lines']
                results['code_lines'] = max(0, results['code_lines'])

        except Exception as e:
            print(f"  [Warning] Could not analyze file {file_path}: {e}")
            return None

        return results
    
    def _extract_python_docstring(self, lines: List[str], start_idx: int) -> Optional[str]:
        """
        Extract docstring from Python function or class definition.
        
        Args:
            lines: List of code lines
            start_idx: Index of the function/class definition line
            
        Returns:
            Extracted docstring or None if not found
        """
        if start_idx + 1 >= len(lines):
            return None
            
        # Check for docstring in the next few lines
        for i in start_idx + 1, min(start_idx + 4, len(lines)):
            line = lines[i].strip()
            
            # Single line docstring
            if line.startswith('"""') and line.endswith('"""') and len(line) > 6:
                return line[3:-3].strip()
                
            # Start of multi-line docstring
            if line.startswith('"""'):
                docstring_lines = []
                for j in range(i, len(lines)):
                    docstring_lines.append(lines[j].strip())
                    if j > i and '"""' in lines[j]:
                        # Join all lines and clean up
                        full_docstring = ' '.join(docstring_lines)
                        return full_docstring.replace('"""', '').strip()
                        
        return None
    
    def analyze_codebase(self, codebase_path: str) -> Dict[str, Any]:
        """
        Analyzes an entire codebase directory.

        Args:
            codebase_path: The root path of the codebase

        Returns:
            Dictionary containing the overall analysis results
        """
        overall_results = {
            'total_files': 0,
            'file_counts_by_ext': collections.defaultdict(int),
            'total_lines': 0,
            'total_blank_lines': 0,
            'total_comment_lines': 0,
            'total_code_lines': 0,
            'all_functions': collections.defaultdict(list),
            'all_classes': collections.defaultdict(list),
            'all_imports': collections.defaultdict(list),
            'top_level_dirs': [],
            'top_level_files': [],
            'file_details': {},  # Will store detailed results for each file
            'analysis_errors': 0
        }

        # Get top-level items first
        try:
            for item in os.listdir(codebase_path):
                item_path = os.path.join(codebase_path, item)
                if os.path.isdir(item_path):
                    if item not in self.ignore_dirs:
                        overall_results['top_level_dirs'].append(item)
                elif os.path.isfile(item_path):
                    if item not in self.ignore_files:
                        overall_results['top_level_files'].append(item)
        except Exception as e:
            print(f"[Error] Could not list top-level directory: {e}")

        # Walk through the directory tree
        for root, dirs, files in os.walk(codebase_path, topdown=True):
            # Modify dirs in-place to skip ignored directories
            dirs[:] = [d for d in dirs if d not in self.ignore_dirs]

            for filename in files:
                if filename in self.ignore_files:
                    continue

                file_path = os.path.join(root, filename)
                _, extension = os.path.splitext(filename)
                extension = extension.lower() if extension else '.no_extension'

                overall_results['total_files'] += 1
                overall_results['file_counts_by_ext'][extension] += 1

                # Perform detailed analysis only on supported file types
                if extension in SUPPORTED_EXTENSIONS:
                    file_results = self.analyze_file(file_path)
                    if file_results:
                        # Store detailed results for this file
                        overall_results['file_details'][file_path] = file_results
                        
                        # Update aggregate counts
                        overall_results['total_lines'] += file_results['total_lines']
                        overall_results['total_blank_lines'] += file_results['blank_lines']
                        overall_results['total_comment_lines'] += file_results['comment_lines']
                        overall_results['total_code_lines'] += file_results['code_lines']
                        
                        # Store functions and classes
                        if file_results['functions']:
                            overall_results['all_functions'][file_path].extend(file_results['functions'])
                        if file_results['classes']:
                            overall_results['all_classes'][file_path].extend(file_results['classes'])
                        if file_results.get('imports'):
                            overall_results['all_imports'][file_path].extend(file_results['imports'])
                    else:
                        overall_results['analysis_errors'] += 1

        return overall_results
    
    def get_file_structure(self, file_path: str) -> Dict[str, Any]:
        """
        Get detailed structure information for a single file.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            Dictionary with detailed file structure information
        """
        analysis = self.analyze_file(file_path)
        if not analysis:
            return {'error': 'File could not be analyzed'}
            
        # Extract key structural elements
        structure = {
            'path': file_path,
            'language': detect_language(file_path),  # Use detect_language from language_detector.py
            'metrics': {
                'total_lines': analysis['total_lines'],
                'code_lines': analysis['code_lines'],
                'comment_lines': analysis['comment_lines'],
                'blank_lines': analysis['blank_lines'],
                'comment_ratio': analysis['comment_lines'] / max(1, analysis['total_lines'])
            },
            'functions': analysis['functions'],
            'classes': analysis['classes'],
            'imports': analysis.get('imports', []),
            'variables': analysis.get('variables', []),
            'complexity_points': analysis.get('complexity_points', [])
        }
        
        return structure
