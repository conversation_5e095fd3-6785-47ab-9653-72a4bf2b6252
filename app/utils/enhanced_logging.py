"""
Enhanced logging utilities - ADDITIVE to existing logging
This module adds file-based logging, timing, and structured logging WITHOUT changing existing console logs
"""
import logging
import logging.handlers
import json
import time
import os
from datetime import datetime
from typing import Dict, Any, Optional
import uuid

class TimingLogger:
    """Add timing capabilities to existing loggers"""
    
    def __init__(self, logger_name: str = "timing"):
        self.logger = logging.getLogger(f"{logger_name}.timing")
        self.setup_file_logging()
        self.timers: Dict[str, float] = {}
    
    def setup_file_logging(self):
        """Setup file-based logging for timing data"""
        # Create logs directory
        os.makedirs("logs", exist_ok=True)
        
        # File handler for timing logs
        timing_handler = logging.handlers.RotatingFileHandler(
            'logs/timing.log',
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        timing_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        ))
        timing_handler.setLevel(logging.DEBUG)
        self.logger.addHandler(timing_handler)
        self.logger.setLevel(logging.DEBUG)
    
    def start_timer(self, operation_id: str) -> str:
        """Start timing an operation"""
        self.timers[operation_id] = time.time()
        self.logger.info(f"TIMER_START: {operation_id}")
        return operation_id
    
    def end_timer(self, operation_id: str, **extra_data) -> float:
        """End timing an operation and log duration"""
        if operation_id not in self.timers:
            self.logger.warning(f"TIMER_NOT_FOUND: {operation_id}")
            return 0.0
        
        duration = time.time() - self.timers[operation_id]
        del self.timers[operation_id]
        
        log_data = {
            'operation': operation_id,
            'duration': duration,
            **extra_data
        }
        
        self.logger.info(f"TIMER_END: {json.dumps(log_data)}")
        return duration

class StructuredLogger:
    """Add structured logging capabilities"""
    
    def __init__(self, logger_name: str = "structured"):
        self.logger = logging.getLogger(f"{logger_name}.structured")
        self.setup_file_logging()
    
    def setup_file_logging(self):
        """Setup file-based structured logging"""
        os.makedirs("logs", exist_ok=True)
        
        # Structured data handler
        structured_handler = logging.handlers.RotatingFileHandler(
            'logs/structured.log',
            maxBytes=50*1024*1024,  # 50MB
            backupCount=10
        )
        structured_handler.setFormatter(logging.Formatter('%(message)s'))
        structured_handler.setLevel(logging.DEBUG)
        self.logger.addHandler(structured_handler)
        self.logger.setLevel(logging.DEBUG)
    
    def log_event(self, event_type: str, **data):
        """Log a structured event"""
        event = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            **data
        }
        self.logger.info(json.dumps(event))

class ProcessingLogger:
    """Enhanced logging for file processing operations"""
    
    def __init__(self):
        self.timing = TimingLogger("processing")
        self.structured = StructuredLogger("processing")
        self.session_id = str(uuid.uuid4())[:8]
    
    def log_batch_start(self, total_files: int, provider: str, output_dir: str):
        """Log batch processing start"""
        self.structured.log_event(
            "BATCH_START",
            session_id=self.session_id,
            total_files=total_files,
            provider=provider,
            output_dir=output_dir
        )
        self.timing.start_timer(f"batch_{self.session_id}")
    
    def log_file_start(self, file_path: str, language: str, file_size: int):
        """Log individual file processing start"""
        file_id = f"file_{hash(file_path)}_{int(time.time())}"
        self.structured.log_event(
            "FILE_START",
            session_id=self.session_id,
            file_id=file_id,
            file_path=file_path,
            language=language,
            file_size=file_size
        )
        self.timing.start_timer(file_id)
        return file_id
    
    def log_llm_call(self, file_id: str, provider: str, prompt_size: int):
        """Log LLM call start"""
        llm_id = f"llm_{file_id}"
        self.structured.log_event(
            "LLM_CALL_START",
            session_id=self.session_id,
            file_id=file_id,
            llm_id=llm_id,
            provider=provider,
            prompt_size=prompt_size
        )
        self.timing.start_timer(llm_id)
        return llm_id
    
    def log_llm_response(self, llm_id: str, file_id: str, success: bool, comment_count: int, error: str = None):
        """Log LLM response"""
        duration = self.timing.end_timer(llm_id)
        self.structured.log_event(
            "LLM_CALL_END",
            session_id=self.session_id,
            file_id=file_id,
            llm_id=llm_id,
            success=success,
            comment_count=comment_count,
            duration=duration,
            error=error
        )
    
    def log_file_complete(self, file_id: str, file_path: str, success: bool, comment_count: int):
        """Log file processing completion"""
        duration = self.timing.end_timer(file_id)
        self.structured.log_event(
            "FILE_COMPLETE",
            session_id=self.session_id,
            file_id=file_id,
            file_path=file_path,
            success=success,
            comment_count=comment_count,
            duration=duration
        )
    
    def log_batch_complete(self, processed_files: int, total_files: int):
        """Log batch processing completion"""
        duration = self.timing.end_timer(f"batch_{self.session_id}")
        success_rate = (processed_files / total_files * 100) if total_files > 0 else 0
        
        self.structured.log_event(
            "BATCH_COMPLETE",
            session_id=self.session_id,
            processed_files=processed_files,
            total_files=total_files,
            success_rate=success_rate,
            total_duration=duration
        )

# Global instances for easy access
processing_logger = ProcessingLogger()
timing_logger = TimingLogger("global")
structured_logger = StructuredLogger("global")
