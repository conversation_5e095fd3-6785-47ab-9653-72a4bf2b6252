"""
Dedicated Doxygen Comment Generator Module
Implements comprehensive Doxygen-compatible comment generation for multiple programming languages.
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class DoxygenCommentGenerator:
    """
    Specialized generator for creating Doxygen-compatible comments across multiple programming languages.
    Supports language-specific syntax and comprehensive Doxygen tag generation.
    """
    
    # Language-specific comment syntax mapping
    LANGUAGE_SYNTAX = {
        'cpp': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'c++': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'c': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'h': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'hpp': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'python': {'block_start': '##', 'line_prefix': '## ', 'block_end': ''},
        'py': {'block_start': '##', 'line_prefix': '## ', 'block_end': ''},
        'javascript': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'js': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'typescript': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'ts': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'java': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'php': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'go': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'rust': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
        'rs': {'block_start': '/**', 'line_prefix': ' * ', 'block_end': ' */'},
    }
    
    # Doxygen tags organized by context
    DOXYGEN_TAGS = {
        'file_header': ['@file', '@brief', '@author', '@date', '@version', '@copyright'],
        'class': ['@class', '@brief', '@details', '@author', '@date', '@see'],
        'function': ['@brief', '@param', '@return', '@throws', '@details', '@note', '@see'],
        'method': ['@brief', '@param', '@return', '@throws', '@details', '@note'],
        'variable': ['@brief', '@details'],
        'constant': ['@brief', '@details'],
        'namespace': ['@namespace', '@brief', '@details'],
        'enum': ['@enum', '@brief', '@details'],
        'struct': ['@struct', '@brief', '@details'],
        'typedef': ['@typedef', '@brief', '@details'],
        'macro': ['@def', '@brief', '@param', '@details']
    }

    @classmethod
    def get_language_syntax(cls, language: str) -> Dict[str, str]:
        """Get comment syntax configuration for the specified language."""
        lang_key = language.lower().replace('.', '')
        return cls.LANGUAGE_SYNTAX.get(lang_key, cls.LANGUAGE_SYNTAX['cpp'])

    @classmethod
    def generate_doxygen_prompt(cls, code: str, language: str, file_path: str = "") -> str:
        """
        Generate a comprehensive Doxygen comment generation prompt tailored for the specific language.
        
        Args:
            code: Source code to analyze
            language: Programming language
            file_path: Path to the file (for @file tag)
            
        Returns:
            Formatted prompt for LLM
        """
        syntax = cls.get_language_syntax(language)
        file_name = os.path.basename(file_path) if file_path else f"example.{language}"
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        # Language-specific examples
        examples = cls._get_language_examples(language, syntax, file_name)
        
        prompt = f"""
You are a professional software documentation expert specializing in Doxygen documentation standards.

TASK: Analyze the following {language} code and generate comprehensive Doxygen-compatible comments.

LANGUAGE: {language}
FILE: {file_name}
COMMENT SYNTAX: {syntax['block_start']} ... {syntax['block_end']}

CRITICAL REQUIREMENTS:
1. Use ONLY {language}-appropriate Doxygen comment syntax
2. Include proper Doxygen tags (@brief, @param, @return, @file, @class, etc.)
3. Focus on documenting PURPOSE, BEHAVIOR, and API CONTRACT
4. Use professional, technical language suitable for API documentation
5. Generate comments that would pass Doxygen validation

DOXYGEN COMMENT STRUCTURE FOR {language.upper()}:
{examples['structure']}

REQUIRED DOCUMENTATION ELEMENTS:
1. FILE HEADER: Add at line 1 with @file, @brief, @author, @date
2. CLASSES: Document with @class, @brief, @details
3. FUNCTIONS/METHODS: Document with @brief, @param (for each parameter), @return
4. COMPLEX LOGIC: Add @details or @note for non-obvious implementations
5. CONSTANTS/VARIABLES: Document with @brief when significant

EXAMPLE OUTPUTS:
{examples['file_header']}

{examples['function']}

{examples['class']}

CODE TO ANALYZE:
```{language}
{code}
```

RESPONSE FORMAT:
Respond with a valid JSON object containing a "comments" array. Each comment must specify:
- line_number: Line where comment should be inserted (1-based)
- comment: Complete Doxygen comment block with proper {language} syntax
- type: Type of element being documented (file, class, function, variable, etc.)

Example JSON structure:
{{
    "comments": [
        {{
            "line_number": 1,
            "comment": "{syntax['block_start']}\\n{syntax['line_prefix']}@file {file_name}\\n{syntax['line_prefix']}@brief Brief description of file purpose\\n{syntax['line_prefix']}<AUTHOR> Documentation System\\n{syntax['line_prefix']}@date {current_date}\\n{syntax['block_end']}",
            "type": "file"
        }},
        {{
            "line_number": 15,
            "comment": "{syntax['block_start']}\\n{syntax['line_prefix']}@brief Brief function description\\n{syntax['line_prefix']}@param param_name Parameter description\\n{syntax['line_prefix']}@return Return value description\\n{syntax['block_end']}",
            "type": "function"
        }}
    ]
}}

DOCUMENTATION STANDARDS:
- Be concise but comprehensive
- Focus on API contract and behavior
- Avoid redundant comments for self-explanatory code
- Use consistent terminology throughout
- Include @param for ALL function parameters
- Include @return for ALL non-void functions
- Add @throws/@exception for functions that can throw
- Use @details for complex algorithms or important implementation notes

Generate professional-quality Doxygen documentation suitable for API reference.
"""
        return prompt

    @classmethod
    def _get_language_examples(cls, language: str, syntax: Dict[str, str], file_name: str) -> Dict[str, str]:
        """Generate language-specific Doxygen comment examples."""
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        examples = {
            'structure': f"""
Block Comment: {syntax['block_start']}
Line Prefix:   {syntax['line_prefix']}
Block End:     {syntax['block_end']}
""",
            
            'file_header': f"""
{syntax['block_start']}
{syntax['line_prefix']}@file {file_name}
{syntax['line_prefix']}@brief Brief description of the file's purpose
{syntax['line_prefix']}<AUTHOR> Documentation System
{syntax['line_prefix']}@date {current_date}
{syntax['line_prefix']}@version 1.0
{syntax['block_end']}""",
            
            'function': f"""
{syntax['block_start']}
{syntax['line_prefix']}@brief Brief description of function purpose
{syntax['line_prefix']}@param param1 Description of first parameter
{syntax['line_prefix']}@param param2 Description of second parameter
{syntax['line_prefix']}@return Description of return value
{syntax['line_prefix']}@details Additional implementation details if needed
{syntax['block_end']}""",
            
            'class': f"""
{syntax['block_start']}
{syntax['line_prefix']}@class ClassName
{syntax['line_prefix']}@brief Brief description of class purpose
{syntax['line_prefix']}@details Detailed explanation of class functionality and usage
{syntax['block_end']}"""
        }
        
        return examples

    @classmethod
    def validate_doxygen_comment(cls, comment: str, language: str) -> bool:
        """
        Validate that a comment follows proper Doxygen syntax for the given language.
        
        Args:
            comment: Comment text to validate
            language: Programming language
            
        Returns:
            True if comment is valid Doxygen format
        """
        syntax = cls.get_language_syntax(language)
        
        # Check basic structure
        if not comment.strip().startswith(syntax['block_start']):
            return False
            
        # Check for at least one Doxygen tag
        doxygen_tags = ['@file', '@brief', '@param', '@return', '@class', '@namespace', 
                       '@details', '@author', '@date', '@version', '@see', '@note']
        has_doxygen_tag = any(tag in comment for tag in doxygen_tags)
        
        return has_doxygen_tag

    @classmethod
    def format_comment_for_language(cls, comment: str, language: str) -> str:
        """
        Ensure comment is properly formatted for the target language.
        
        Args:
            comment: Raw comment text
            language: Target programming language
            
        Returns:
            Properly formatted comment
        """
        syntax = cls.get_language_syntax(language)
        
        # If comment doesn't start with proper syntax, wrap it
        if not comment.strip().startswith(syntax['block_start']):
            lines = comment.strip().split('\n')
            formatted_lines = [syntax['block_start']]
            
            for line in lines:
                if line.strip():
                    formatted_lines.append(f"{syntax['line_prefix']}{line.strip()}")
                else:
                    formatted_lines.append(syntax['line_prefix'].rstrip())
                    
            if syntax['block_end']:
                formatted_lines.append(syntax['block_end'])
                
            return '\n'.join(formatted_lines)
        
        return comment

    @classmethod
    def get_supported_languages(cls) -> List[str]:
        """Get list of supported programming languages."""
        return list(cls.LANGUAGE_SYNTAX.keys())

    @classmethod
    def get_language_info(cls, language: str) -> Dict[str, Any]:
        """
        Get comprehensive information about Doxygen support for a specific language.
        
        Args:
            language: Programming language
            
        Returns:
            Dictionary with syntax info, supported tags, and examples
        """
        syntax = cls.get_language_syntax(language)
        examples = cls._get_language_examples(language, syntax, f"example.{language}")
        
        return {
            'language': language,
            'syntax': syntax,
            'supported_tags': cls.DOXYGEN_TAGS,
            'examples': examples,
            'is_supported': language.lower() in cls.LANGUAGE_SYNTAX
        }
