import os
import yaml
import json
import logging
import asyncio
import time
import aiohttp
from typing import List, Dict, Any
from dotenv import load_dotenv
from app.llm.base import BaseDocGenerator

load_dotenv()
logger = logging.getLogger(__name__)

class GeminiDocGenerator(BaseDocGenerator):
    """
    Documentation generator using Google's Gemini API via HTTP requests.
    Focuses on inline comments.
    """
    def __init__(self):
        """Initialize the Gemini client."""
        try:
            with open('config/config.yaml', 'r') as f:
                config_data = yaml.safe_load(f)

            self.api_key = os.getenv("GEMINI_API_KEY")

            if not self.api_key:
                raise ValueError("GEMINI_API_KEY not found in environment variables")

            # Rate limiting
            self.last_request_time = 0
            self.min_request_interval = 2.0  # 2 seconds between requests for Gemini
            self.max_retries = 3

            # Get available models and let user choose
            self.model_name = self._select_model(config_data)

            # Gemini REST API endpoint
            self.api_url = f"https://generativelanguage.googleapis.com/v1beta/models/{self.model_name}:generateContent"

            logger.info(f"✅ Initialized Gemini successfully!")
            logger.info(f"🤖 Using model: {self.model_name}")
            print(f"🤖 Gemini model selected: {self.model_name}")

        except Exception as e:
            logger.error(f"Failed to initialize Gemini client: {e}", exc_info=True)
            raise

    async def _get_available_models(self):
        """Get list of available Gemini models."""
        try:
            models_url = f"https://generativelanguage.googleapis.com/v1beta/models?key={self.api_key}"

            async with aiohttp.ClientSession() as session:
                async with session.get(models_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = []
                        if 'models' in data:
                            for model in data['models']:
                                model_name = model.get('name', '').replace('models/', '')
                                # Filter for text generation models
                                if 'generateContent' in model.get('supportedGenerationMethods', []):
                                    models.append({
                                        'name': model_name,
                                        'display_name': model.get('displayName', model_name),
                                        'description': model.get('description', 'No description available')
                                    })
                        return models
                    else:
                        logger.warning(f"Failed to fetch models: {response.status}")
                        return []
        except Exception as e:
            logger.warning(f"Error fetching available models: {e}")
            return []

    def _select_model(self, config_data):
        """Select the best available model with user interaction."""
        # Get configured model name as fallback
        configured_model = config_data.get('llm', {}).get('models', {}).get('gemini', {}).get('model_name', "gemini-1.5-flash")

        # Try to get available models
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            available_models = loop.run_until_complete(self._get_available_models())
            loop.close()

            if not available_models:
                logger.warning("Could not fetch available models, using configured model")
                print(f"🤖 Using fallback model: {configured_model}")
                return configured_model

            print(f"\n🔍 Found {len(available_models)} available Gemini models")

            # Show popular models first for easy selection
            popular_models = []
            other_models = []

            popular_names = [
                'gemini-2.5-flash', 'gemini-2.5-pro',
                'gemini-2.0-flash', 'gemini-2.0-pro-exp',
                'gemini-1.5-flash', 'gemini-1.5-pro'
            ]

            for model in available_models:
                if any(popular in model['name'] for popular in popular_names):
                    popular_models.append(model)
                else:
                    other_models.append(model)

            # Sort popular models by preference
            popular_models.sort(key=lambda x: next((i for i, p in enumerate(popular_names) if p in x['name']), 999))

            print("\n📋 Select a Gemini model:")
            print("   Popular models:")

            all_options = []
            option_num = 1

            # Show popular models first
            for model in popular_models[:8]:  # Show top 8 popular models
                marker = "✅ (configured)" if model['name'] == configured_model else "  "
                print(f"   {option_num:2d}. {model['name']} - {model['display_name']} {marker}")
                all_options.append(model)
                option_num += 1

            # Option to see all models
            print(f"   {option_num:2d}. 📋 Show all {len(available_models)} models")
            show_all_option = option_num
            option_num += 1

            # Auto-select option
            print(f"   {option_num:2d}. 🎯 Auto-select best model")
            auto_select_option = option_num

            # Get user choice
            while True:
                try:
                    choice = input(f"\nSelect model (1-{option_num}, or Enter for auto-select): ").strip()

                    if not choice:  # Enter pressed - auto select
                        choice = str(auto_select_option)

                    choice_num = int(choice)

                    if 1 <= choice_num <= len(all_options):
                        selected_model = all_options[choice_num - 1]['name']
                        print(f"🎯 Selected: {selected_model}")
                        return selected_model

                    elif choice_num == show_all_option:
                        return self._show_all_models_selection(available_models, configured_model)

                    elif choice_num == auto_select_option:
                        return self._auto_select_model(available_models, configured_model)

                    else:
                        print(f"❌ Invalid choice. Please enter 1-{option_num}")

                except ValueError:
                    print("❌ Invalid input. Please enter a number.")
                except KeyboardInterrupt:
                    print(f"\n🎯 Using auto-selection...")
                    return self._auto_select_model(available_models, configured_model)

        except Exception as e:
            logger.warning(f"Error during model selection: {e}")

        # Fallback to configured model
        print(f"🤖 Using fallback model: {configured_model}")
        return configured_model

    def _show_all_models_selection(self, available_models, configured_model):
        """Show all available models for selection."""
        print(f"\n📋 All {len(available_models)} available Gemini models:")

        for i, model in enumerate(available_models, 1):
            marker = "✅ (configured)" if model['name'] == configured_model else "  "
            print(f"   {i:2d}. {model['name']} - {model['display_name']} {marker}")

        print(f"   {len(available_models) + 1:2d}. 🎯 Auto-select best model")
        auto_option = len(available_models) + 1

        while True:
            try:
                choice = input(f"\nSelect model (1-{auto_option}, or Enter for auto-select): ").strip()

                if not choice:
                    choice = str(auto_option)

                choice_num = int(choice)

                if 1 <= choice_num <= len(available_models):
                    selected_model = available_models[choice_num - 1]['name']
                    print(f"🎯 Selected: {selected_model}")
                    return selected_model

                elif choice_num == auto_option:
                    return self._auto_select_model(available_models, configured_model)

                else:
                    print(f"❌ Invalid choice. Please enter 1-{auto_option}")

            except ValueError:
                print("❌ Invalid input. Please enter a number.")
            except KeyboardInterrupt:
                print(f"\n🎯 Using auto-selection...")
                return self._auto_select_model(available_models, configured_model)

    def _auto_select_model(self, available_models, configured_model):
        """Auto-select the best available model."""
        # Check if configured model is available first
        for model in available_models:
            if model['name'] == configured_model:
                print(f"🎯 Auto-selected configured model: {configured_model}")
                return configured_model

        # Try preferred models in order
        preferred_models = [
            'gemini-2.5-flash', 'gemini-2.5-pro',
            'gemini-2.0-flash', 'gemini-1.5-flash',
            'gemini-1.5-pro', 'gemini-pro'
        ]

        for preferred in preferred_models:
            for model in available_models:
                if preferred in model['name']:
                    print(f"🎯 Auto-selected: {model['name']}")
                    return model['name']

        # If no preferred model found, use the first available
        if available_models:
            selected = available_models[0]['name']
            print(f"🎯 Auto-selected first available: {selected}")
            return selected

        # Final fallback
        print(f"🎯 Using fallback: {configured_model}")
        return configured_model

    async def _rate_limit(self):
        """Implement rate limiting between API calls."""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time

        if time_since_last_request < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last_request
            logger.info(f"Gemini rate limiting: waiting {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)

        self.last_request_time = time.time()

    async def generate_inline_comments(self, code: str, language: str) -> List[Dict[str, Any]]:
        """
        Generate inline comments for code blocks using google-generativeai==0.1.0rc1.
        Includes rate limiting and retry logic.
        """
        await self._rate_limit()

        # Log input details
        code_lines = len(code.split('\n'))
        code_chars = len(code)
        logger.info(f"🚀 GEMINI REQUEST - Language: {language}, Lines: {code_lines}, Characters: {code_chars}")
        logger.debug(f"📝 GEMINI INPUT CODE:\n{'-'*50}\n{code}\n{'-'*50}")
        prompt = f"""
        Analyze this {language} code and generate meaningful inline comments that explain:
        - Complex logic and algorithms
        - Non-obvious implementation details
        - Important technical decisions

        Code:
        {code}

        IMPORTANT: You MUST format your response as a valid JSON object with a "comments" array like this:
        {{
            "comments": [
                {{"line_number": 10, "comment": "Initialize the database connection pool"}},
                {{"line_number": 15, "comment": "Execute the main query with retry logic"}}
            ]
        }}
        Ensure the entire response is ONLY this JSON object and nothing else.

        Guidelines:
        - Focus on explaining WHY, not WHAT
        - Only comment non-obvious code
        - Keep comments brief and technical
        - Skip obvious or self-documenting code
        """

        # Log the full prompt being sent
        prompt_chars = len(prompt)
        logger.info(f"📤 GEMINI PROMPT - Characters: {prompt_chars}")
        logger.debug(f"📤 GEMINI FULL PROMPT:\n{'-'*50}\n{prompt}\n{'-'*50}")

        for attempt in range(self.max_retries):
            try:
                # Use HTTP requests to call Gemini REST API
                headers = {
                    'Content-Type': 'application/json',
                }

                # Prepare the request payload
                payload = {
                    "contents": [{
                        "parts": [{
                            "text": prompt
                        }]
                    }],
                    "generationConfig": {
                        "temperature": 0.1,
                        "maxOutputTokens": 2048,
                    }
                }

                # Make the API call
                async with aiohttp.ClientSession() as session:
                    url_with_key = f"{self.api_url}?key={self.api_key}"
                    async with session.post(url_with_key, headers=headers, json=payload) as response:
                        if response.status == 200:
                            response_data = await response.json()

                            # Extract text from response
                            if 'candidates' in response_data and response_data['candidates']:
                                candidate = response_data['candidates'][0]
                                if 'content' in candidate and 'parts' in candidate['content']:
                                    response_text = candidate['content']['parts'][0].get('text', '')
                                else:
                                    logger.warning("No content found in Gemini response")
                                    if attempt == self.max_retries - 1:
                                        return []
                                    continue
                            else:
                                logger.warning("No candidates found in Gemini response")
                                if attempt == self.max_retries - 1:
                                    return []
                                continue
                        else:
                            error_text = await response.text()
                            logger.warning(f"Gemini API error {response.status}: {error_text}")
                            if attempt == self.max_retries - 1:
                                return []
                            continue

                # Log the raw response
                response_chars = len(response_text) if response_text else 0
                logger.info(f"📥 GEMINI RESPONSE - Characters: {response_chars}")
                logger.debug(f"📥 GEMINI RAW RESPONSE:\n{'-'*50}\n{response_text}\n{'-'*50}")

                # Try to parse the JSON response
                try:
                    # It's common for LLMs to wrap JSON in ```json ... ```, try to strip it.
                    if response_text.strip().startswith("```json"):
                        response_text = response_text.strip()[7:]
                        if response_text.strip().endswith("```"):
                            response_text = response_text.strip()[:-3]

                    parsed = json.loads(response_text.strip())

                    if isinstance(parsed, dict) and "comments" in parsed:
                        comments_list = parsed["comments"]
                        if isinstance(comments_list, list):
                            valid_comments = []
                            for item in comments_list:
                                if (isinstance(item, dict) and
                                        "line_number" in item and isinstance(item["line_number"], int) and
                                        "comment" in item and isinstance(item["comment"], str)):
                                    valid_comments.append(item)
                                else:
                                    logger.warning(f"Invalid comment item format or type in Gemini response: {item}")
                            logger.info(f"✅ GEMINI SUCCESS - Generated {len(valid_comments)} valid comments")
                            logger.debug(f"✅ GEMINI PARSED COMMENTS:\n{json.dumps(valid_comments, indent=2)}")
                            return valid_comments
                        else:
                            logger.warning(f"'comments' key in Gemini JSON is not a list: {parsed.get('comments')}")
                            if attempt == self.max_retries - 1:
                                return []
                            continue
                    else:
                        logger.warning(f"Unexpected JSON structure or 'comments' key missing in Gemini response: {parsed}")
                        if attempt == self.max_retries - 1:
                            return []
                        continue

                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON response from Gemini: {e}")
                    logger.debug(f"Raw response text from Gemini for JSON parsing: '{response_text}'")
                    if attempt == self.max_retries - 1:
                        return []
                    continue

            except Exception as e:
                logger.warning(f"Gemini attempt {attempt + 1}/{self.max_retries} failed: {e}")
                if attempt == self.max_retries - 1:
                    logger.error(f"All {self.max_retries} Gemini attempts failed")
                    return []

                # Exponential backoff for retries
                wait_time = 2 ** attempt
                logger.info(f"Retrying Gemini in {wait_time} seconds...")
                await asyncio.sleep(wait_time)

        return []
