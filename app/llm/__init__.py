"""
LLM package initializer.
This file makes the 'llm' directory a Python package and exposes
key components like the LLM generator factory.
"""
from .base import LLMFactory, BaseDocGenerator
from .gemini_handler import GeminiDocGenerator
# OpenAICompatibleGenerator handles OpenAI, Groq, and OpenRouter
from .openai_handler import OpenAICompatibleGenerator 

def get_llm_generator(provider: str = None) -> BaseDocGenerator:
    """
    Factory function to get an instance of an LLM document generator.
    This is a convenience wrapper around LLMFactory.create().
    If no provider is specified, the factory will attempt to determine one
    from environment variables (OpenAI, Groq, Gemini).
    """
    return LLMFactory.create(provider)

# Register the available providers with the factory.
# The OpenAICompatibleGenerator's __init__ method handles specific
# configurations for "openai", "groq", and "openrouter".
# The LLMFactory.create method in base.py will instantiate these.
# When LLMFactory.create('openai') is called, it will instantiate OpenAICompatibleGenerator()
# and its __init__ will try to pick 'openai' if OPENAI_API_KEY is set, or another if specified.
# This registration maps the provider name to the class that can handle it.
LLMFactory.register("gemini", GeminiDocGenerator)
LLMFactory.register("openai", OpenAICompatibleGenerator) 
LLMFactory.register("groq", OpenAICompatibleGenerator) # Groq is handled by OpenAICompatibleGenerator
LLMFactory.register("openrouter", OpenAICompatibleGenerator) # OpenRouter is also handled

# Note: The GroqDocGenerator from groq_handler.py is removed as OpenAICompatibleGenerator covers Groq.
# If groq_handler.py is empty or unused, it can be deleted.

__all__ = [
    "get_llm_generator",
    "LLMFactory",
    "BaseDocGenerator",
    "GeminiDocGenerator",
    "OpenAICompatibleGenerator", # Expose the actual class name
]
