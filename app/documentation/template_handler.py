from typing import Dict, List, Any
import os
import json
import tempfile
from jinja2 import Environment, FileSystemLoader
from app.utils.language_detector import detect_language
from app.utils.code_analyzer import CodeAnalyzer

class TemplateDocGenerator:
    def __init__(self, templates_dir: str, output_dir: str):
        self.templates_dir = templates_dir
        self.output_dir = output_dir
        self.env = Environment(loader=FileSystemLoader(templates_dir))
        self.analyzer = CodeAnalyzer()

    async def generate_inline_comments(self, code: str, language: str, context: Dict[str, Any] = None) -> List[Dict[str, any]]:
        """
        Generate Doxygen-compatible inline comments for code blocks.

        Args:
            code: The code to generate comments for
            language: The programming language of the code
            context: Additional context information about the code

        Returns:
            List of {'line_number': int, 'comment': str}
        """
        if context is None:
            context = {}

        # Render the prompt template
        template = self.env.get_template('prompt.j2')
        prompt = template.render(
            file_content=code,
            language=language,
            context_json=json.dumps(context)
        )

        # For a template-based generator, we'll create basic Doxygen comments
        # In a real LLM implementation, this would send the prompt to the LLM
        comments = []

        # Generate comments based on the language and context
        if 'functions' in context:
            for func in context.get('functions', []):
                line_num = func.get('line', 1)
                func_name = func.get('name', 'unknown')

                if language == 'Python':
                    comments.append({
                        "line_number": line_num,
                        "comment": f'"""!\n@brief Function {func_name}\n@param args Description of parameters\n@return Description of return value\n"""'
                    })
                else:  # C++, Java, JavaScript, etc.
                    comments.append({
                        "line_number": line_num,
                        "comment": f'/**\n * @brief Function {func_name}\n * @param args Description of parameters\n * @return Description of return value\n */'
                    })

        if 'classes' in context:
            for cls in context.get('classes', []):
                line_num = cls.get('line', 1)
                class_name = cls.get('name', 'unknown')

                if language == 'Python':
                    comments.append({
                        "line_number": line_num,
                        "comment": f'"""!\n@brief Class {class_name}\n"""'
                    })
                else:  # C++, Java, JavaScript, etc.
                    comments.append({
                        "line_number": line_num,
                        "comment": f'/**\n * @brief Class {class_name}\n */'
                    })

        return comments

    def _save_temp_file(self, content: str) -> str:
        """
        Save content to a temporary file for analysis.

        Args:
            content: The content to save

        Returns:
            Path to the temporary file
        """
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.tmp')
        temp_file.write(content.encode('utf-8'))
        temp_file.close()
        return temp_file.name
