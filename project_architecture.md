# Project Architecture Plan (Generated: 2025-04-23)

**Project Goal:** To automatically generate documentation (inline comments and summaries) for code repositories using AI (Groq LLM) and integrate with Git for fetching code and potentially pushing updates.

**Major Components & Services:**

- **User Interface:** Can be interacted with via a REST API (using FastAPI) or a Command-Line Interface (CLI).
- **API Server (FastAPI/Uvicorn):** Handles HTTP requests, orchestrates the documentation process, and manages state for API calls.
- **Authentication Module (`app/auth.py`):** Manages OAuth2 authentication with GitHub and GitLab to obtain access tokens, primarily for accessing private repositories. Uses `python-jose` and `passlib`.
- **Repository Handler (`app/repo_handler.py`):** Responsible for cloning Git repositories using provided URLs and access tokens, analyzing file structure, and inserting generated comments back into the code files locally.
- **Documentation Generator (`app/llm/*`, `EnhancedCommentGenerator`):** Interfaces with the chosen LLM service (Groq by default) or uses templates to generate file summaries and inline code comments based on the code content.
- **LLM Service (Groq):** The external AI service that performs the core comment and summary generation based on prompts sent by the `DocGenerator`.
- **Git:** The underlying version control system used to clone repositories. The CLI mode also uses Git commands to create branches, commit changes, and push updates.
- **Configuration (`config/config.yaml`, `.env`):** Stores settings like temporary directories, template paths, and sensitive API keys (`GROQ_API_KEY`, OAuth credentials).

**Workflow Diagram:**

```mermaid
sequenceDiagram
    participant User
    participant CLI
    participant FastAPI API
    participant Auth Module (app/auth.py)
    participant OAuth Provider (GitHub/GitLab)
    participant RepoHandler (app/repo_handler.py)
    participant Git
    participant DocGenerator (app/llm/*)
    participant LLM Service (Groq)

    alt API Flow (/docs/generate)
        User->>FastAPI API: POST (repo_url, access_token)
        FastAPI API->>RepoHandler: download_repository(repo_url, token)
        RepoHandler->>Git: clone repository
        Git-->>RepoHandler: repo_path (local temp dir)
        RepoHandler-->>FastAPI API: repo_path
        FastAPI API->>RepoHandler: get_file_structure(repo_path)
        RepoHandler-->>FastAPI API: file_structure
        loop For each code file
            FastAPI API->>DocGenerator: generate_file_documentation(content, path)
            DocGenerator->>LLM Service (Groq): Analyze code & generate docs
            LLM Service (Groq)-->>DocGenerator: Generated comments & summary
            DocGenerator-->>FastAPI API: file_docs
            FastAPI API->>RepoHandler: insert_comments(content, comments)
            RepoHandler-->>FastAPI API: updated_content
            Note right of FastAPI API: Overwrite file locally in repo_path
        end
        FastAPI API->>DocGenerator: generate_repo_summary(structure, file_summaries)
        DocGenerator->>LLM Service (Groq): Generate overall summary
        LLM Service (Groq)-->>DocGenerator: repo_summary
        DocGenerator-->>FastAPI API: repo_summary
        FastAPI API-->>User: JSON Response (repo_summary, status, stats)
        Note right of FastAPI API: Local repo copy is kept, path logged.
    end

    alt CLI Flow (python app/main.py ...)
        User->>CLI: Run command with repo_url, optional token
        opt Token needed & not provided
            CLI->>Auth Module (app/auth.py): Initiate OAuth flow
            Auth Module->>OAuth Provider (GitHub/GitLab): Redirect User via Browser
            User->>OAuth Provider (GitHub/GitLab): Authenticate & Authorize
            OAuth Provider-->>Auth Module (app/auth.py): Callback with auth code
            Auth Module->>OAuth Provider (GitHub/GitLab): Exchange code for token
            OAuth Provider-->>Auth Module (app/auth.py): Access Token
            Auth Module-->>CLI: Return Access Token
        end
        CLI->>RepoHandler: download_repository(repo_url, token)
        RepoHandler->>Git: clone repository
        Git-->>RepoHandler: repo_path (local temp dir)
        RepoHandler-->>CLI: repo_path
        CLI->>RepoHandler: get_file_structure(repo_path)
        RepoHandler-->>CLI: file_structure
        loop For each code file
            CLI->>DocGenerator: generate_file_documentation(content, path)
            DocGenerator->>LLM Service (Groq): Analyze code & generate docs
            LLM Service (Groq)-->>DocGenerator: Generated comments & summary
            DocGenerator-->>CLI: file_docs
            CLI->>RepoHandler: insert_comments(content, comments)
            RepoHandler-->>CLI: updated_content
            Note right of CLI: Overwrite file locally in repo_path
        end
        CLI->>DocGenerator: generate_repo_summary(structure, file_summaries)
        DocGenerator->>LLM Service (Groq): Generate overall summary
        LLM Service (Groq)-->>DocGenerator: repo_summary
        DocGenerator-->>CLI: repo_summary
        Note right of CLI: Update README.md locally in repo_path
        CLI->>Git: checkout -b documentation-update
        CLI->>Git: add .
        CLI->>Git: commit -m "Add documentation"
        CLI->>Git: push origin documentation-update
        Git-->>CLI: Push result status
        CLI->>RepoHandler: cleanup(repo_path)
        RepoHandler->>File System: Delete local repo_path
        CLI-->>User: Log output (Success/Error)
    end

```

**Detailed Steps:**

1.  **Initiation:**
    - **API:** A user sends a POST request to `/docs/generate` with the `repo_url` and an `access_token` (obtained via the separate `/auth/...` flow or provided manually).
    - **CLI:** A user runs `python app/main.py --repo-url <url> [--auth-token <token>]`. If the token is missing, the CLI attempts to initiate an OAuth flow via the browser.
2.  **Authentication (if needed):**
    - The `app/auth.py` module handles the OAuth2 dance with GitHub/GitLab, exchanging authorization codes for access tokens. These tokens are used for cloning private repositories.
3.  **Repository Cloning:**
    - The `RepoHandler` uses the provided `repo_url` and `access_token` to clone the Git repository into a temporary local directory (`config['git']['temp_dir']`).
4.  **Code Analysis:**
    - `RepoHandler` scans the cloned repository to identify all code files and their structure.
5.  **File-Level Documentation:**
    - The application iterates through each identified code file.
    - The file content is read.
    - The `EnhancedCommentGenerator` (using `GroqDocGenerator` or `TemplateDocGenerator`) is called to generate:
      - A summary for the file.
      - Inline comments for functions/classes/methods within the file.
    - This involves sending prompts with the code context to the Groq API (if configured).
    - The `RepoHandler` inserts the generated inline comments back into the code file content _in the local temporary copy_.
6.  **Repository-Level Summary:**
    - After processing all files, the `EnhancedCommentGenerator` is called again to generate an overall repository summary (e.g., for a README file), potentially using the individual file summaries as context. This also likely involves a call to the Groq API.
7.  **Output/Finalization:** - **API:** Returns a JSON response containing the repository summary, status, and processing statistics. The modified local repository copy is _kept_ in the temporary directory, and its path is logged. Cleanup is not automatic. - **CLI:** Updates the `README.md` file in the local temporary copy with the generated summary. It then uses `git` commands to: - Create a new branch (`documentation-update`). - Add all changes (modified code files with comments, updated README). - Commit the changes. - Push the new branch to the remote repository (`origin`). - Finally, the `RepoHandler` cleans up by deleting the temporary local repository directory. Log messages indicate success or failure.
    z
