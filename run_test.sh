#!/bin/bash

# This script creates a controlled experiment to definitively test check_sessions.py

echo "🧪 Starting controlled test..."

# 1. Activate the environment for the main script to use
source activate_ai_agent.sh

# 2. Start a new shell in the background that also activates the environment
echo "🚀 Launching a background shell with the venv active..."
# The background shell will activate the venv and then sleep for 20 seconds
bash -c 'source activate_ai_agent.sh >/dev/null 2>&1 && echo "[Background Shell PID: $$] Venv active. Sleeping..." && sleep 20' &

# Give the background process a moment to start up
sleep 3

echo -e "\n-----------------------------------------------------"
echo "🕵️  Now running the inspector script..."
echo "-----------------------------------------------------"

# 3. Run the checker script and see if it detects the background shell
python check_sessions.py

echo -e "\n-----------------------------------------------------"
echo "✅ Test complete."
